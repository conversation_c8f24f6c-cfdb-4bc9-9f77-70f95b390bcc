{"categories": [{"name": "Basic Concepts", "name_ar": "المفاهيم الأساسية", "topics": [{"title": "Variables and Data Types", "title_ar": "المتغيرات وأنواع البيانات", "content": "Ring supports dynamic typing. Variables can hold strings, numbers, lists, and objects.", "content_ar": "تدعم Ring الكتابة الديناميكية. يمكن للمتغيرات أن تحتوي على نصوص وأرقام وقوائم وكائنات.", "examples": [{"title": "String Variables", "code": "cName = \"Ahmed\"\ncMessage = 'Hello World'\nsee cName + \": \" + cMessage + nl"}, {"title": "Numeric Variables", "code": "nAge = 25\nnPrice = 99.99\nnTotal = nAge + nPrice\nsee \"Total: \" + nTotal + nl"}, {"title": "List Variables", "code": "aNumbers = [1, 2, 3, 4, 5]\naNames = [\"<PERSON>\", \"<PERSON>\", \"<PERSON>\"]\nsee aNumbers[1] + nl\nsee aNames[2] + nl"}]}, {"title": "Functions", "title_ar": "الدوال", "content": "Functions in Ring are defined using the 'func' keyword and can return values.", "content_ar": "تُعرّف الدوال في Ring باستخدام كلمة 'func' ويمكنها إرجاع قيم.", "examples": [{"title": "Simple Function", "code": "func sayHello()\n    see \"Hello from Ring!\" + nl\n\nsay<PERSON>ello()"}, {"title": "Function with Parameters", "code": "func greet(cName)\n    see \"Hello \" + cName + \"!\" + nl\n\ngreet(\"<PERSON>\")"}, {"title": "Function with Return Value", "code": "func add(nNum1, nNum2)\n    return nNum1 + nNum2\n\nnResult = add(5, 3)\nsee \"Result: \" + nResult + nl"}]}, {"title": "Classes and Objects", "title_ar": "الكلاسات والكائنات", "content": "<PERSON> supports object-oriented programming with classes, inheritance, and encapsulation.", "content_ar": "تدعم Ring البرمجة الكائنية مع الكلاسات والوراثة والتغليف.", "examples": [{"title": "Basic Class", "code": "class Person\n    cName = \"\"\n    nAge = 0\n    \n    func init(cName, nAge)\n        this.cName = cName\n        this.nAge = nAge\n    \n    func introduce()\n        see \"I'm \" + cName + \", \" + nAge + \" years old\" + nl\n\noP = new Person(\"<PERSON>\", 30)\noP.introduce()"}]}]}, {"name": "Control Structures", "name_ar": "هياكل التحكم", "topics": [{"title": "Conditional Statements", "title_ar": "العبارات الشرطية", "content": "Ring provides if-else statements for conditional execution.", "content_ar": "توفر Ring عبارات if-else للتنفيذ الشرطي.", "examples": [{"title": "If Statement", "code": "nAge = 18\nif nAge >= 18\n    see \"You are an adult\" + nl\nelse\n    see \"You are a minor\" + nl\nok"}, {"title": "Multiple Conditions", "code": "nScore = 85\nif nScore >= 90\n    see \"Excellent!\" + nl\nelseif nScore >= 80\n    see \"Very Good!\" + nl\nelseif nScore >= 70\n    see \"Good!\" + nl\nelse\n    see \"Need improvement\" + nl\nok"}]}, {"title": "Loops", "title_ar": "الحلقات", "content": "Ring supports for loops, while loops, and for-in loops for iteration.", "content_ar": "تدعم Ring حلقات for وwhile وfor-in للتكرار.", "examples": [{"title": "For Loop", "code": "for i = 1 to 5\n    see \"Number: \" + i + nl\nnext"}, {"title": "While Loop", "code": "i = 1\nwhile i <= 5\n    see \"Count: \" + i + nl\n    i++\nend"}, {"title": "For-In Loop", "code": "aFruits = [\"Apple\", \"Banana\", \"Orange\"]\nfor cFruit in aFruits\n    see \"Fruit: \" + cFruit + nl\nnext"}]}]}, {"name": "Built-in Functions", "name_ar": "الدوال المدمجة", "topics": [{"title": "String Functions", "title_ar": "دوال النصوص", "content": "Ring provides many built-in functions for string manipulation.", "content_ar": "توفر Ring العديد من الدوال المدمجة للتعامل مع النصوص.", "examples": [{"title": "String Operations", "code": "cText = \"Hello World\"\nsee \"Length: \" + len(cText) + nl\nsee \"Upper: \" + upper(cText) + nl\nsee \"Lower: \" + lower(cText) + nl\nsee \"Substring: \" + substr(cText, 1, 5) + nl"}]}, {"title": "List Functions", "title_ar": "دوال القوائم", "content": "Functions for working with lists and arrays.", "content_ar": "دوال للتعامل مع القوائم والمصفوفات.", "examples": [{"title": "List Operations", "code": "aList = [1, 2, 3]\nadd(aList, 4)\nsee \"Length: \" + len(aList) + nl\nsee \"Find 3: \" + find(aList, 3) + nl\ndel(aList, 2)\nsee \"After delete: \" + list2str(aList) + nl"}]}]}, {"name": "Advanced Topics", "name_ar": "المواضيع المتقدمة", "topics": [{"title": "File Operations", "title_ar": "عمليات الملفات", "content": "Reading from and writing to files in Ring.", "content_ar": "قراءة وكتابة الملفات في Ring.", "examples": [{"title": "File I/O", "code": "# Write to file\nwrite(\"test.txt\", \"Hello Ring!\")\n\n# Read from file\ncContent = read(\"test.txt\")\nsee \"File content: \" + cContent + nl\n\n# Check if file exists\nif fexists(\"test.txt\")\n    see \"File exists!\" + nl\nok"}]}, {"title": "Erro<PERSON>", "title_ar": "معالجة الأخطاء", "content": "Using try-catch blocks for error handling.", "content_ar": "استخدام كتل try-catch لمعالجة الأخطاء.", "examples": [{"title": "Try-Catch", "code": "try\n    nResult = 10 / 0  # This will cause an error\n    see \"Result: \" + nResult + nl\ncatch\n    see \"Error occurred: \" + cCatchError + nl\ndone"}]}]}], "quick_reference": {"keywords": [{"keyword": "func", "description": "Define a function", "description_ar": "تعريف دالة"}, {"keyword": "class", "description": "Define a class", "description_ar": "تعريف كلاس"}, {"keyword": "if", "description": "Conditional statement", "description_ar": "عبارة شرطية"}, {"keyword": "for", "description": "For loop", "description_ar": "حلقة for"}, {"keyword": "while", "description": "While loop", "description_ar": "حلقة while"}, {"keyword": "try", "description": "Error handling", "description_ar": "معالجة الأخطاء"}, {"keyword": "load", "description": "Load library", "description_ar": "تحميل مكتبة"}, {"keyword": "see", "description": "Print output", "description_ar": "طباعة المخرجات"}, {"keyword": "return", "description": "Return value", "description_ar": "إرجاع قيمة"}], "operators": [{"operator": "+", "description": "Addition", "description_ar": "الجمع"}, {"operator": "-", "description": "Subtraction", "description_ar": "الطرح"}, {"operator": "*", "description": "Multiplication", "description_ar": "الضرب"}, {"operator": "/", "description": "Division", "description_ar": "القسمة"}, {"operator": "=", "description": "Assignment", "description_ar": "الإسناد"}, {"operator": "==", "description": "Equality", "description_ar": "المساواة"}, {"operator": "!=", "description": "Not equal", "description_ar": "عدم المساواة"}, {"operator": ">", "description": "Greater than", "description_ar": "أك<PERSON>ر من"}, {"operator": "<", "description": "Less than", "description_ar": "أصغر من"}]}}