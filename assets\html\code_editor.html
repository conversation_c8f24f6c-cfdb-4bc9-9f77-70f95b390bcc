<!DOCTYPE html>
<html>
<head>
    <title>Maestro Code Editor</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/eclipse.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/hint/show-hint.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/javascript/javascript.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/hint/show-hint.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/hint/javascript-hint.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/edit/closebrackets.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/edit/matchbrackets.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/fold/foldcode.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/fold/foldgutter.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/fold/brace-fold.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/fold/foldgutter.min.css">
    <link rel="stylesheet" href="../css/code_editor.css">
</head>
<body>
    <div class="editor-toolbar">
        <button class="toolbar-btn" id="new-file-btn">New File</button>
        <button class="toolbar-btn" id="save-file-btn">Save</button>
        <button class="toolbar-btn" id="format-code-btn">Format</button>
        <button class="toolbar-btn" id="analyze-code-btn">Analyze</button>
        <button class="toolbar-btn" id="run-code-btn">Run</button>
        <div style="flex: 1;"></div>
        <button class="toolbar-btn" id="toggle-analysis-btn">Toggle Analysis</button>
        <button class="toolbar-btn" id="theme-toggle-btn">Dark Theme</button>
    </div>
    
    <div class="editor-container">
        <div class="editor-sidebar">
            <div class="sidebar-section">
                <div class="sidebar-header">Code Snippets</div>
                <div class="sidebar-content" id="snippets-list">
                    <!-- Snippets will be loaded here -->
                </div>
            </div>
            
            <div class="sidebar-section">
                <div class="sidebar-header">Project Files</div>
                <div class="sidebar-content" id="files-list">
                    <!-- Project files will be loaded here -->
                </div>
            </div>
        </div>
        
        <div class="editor-main">
            <textarea id="code-editor"></textarea>
            <div class="analysis-panel" id="analysis-panel">
                <div class="analysis-header">Code Analysis</div>
                <div class="analysis-content" id="analysis-content">
                    <!-- Analysis results will be shown here -->
                </div>
            </div>
        </div>
    </div>
    
    <div class="status-bar">
        <span id="cursor-position">Line 1, Column 1</span>
        <span id="file-info">Ring File</span>
        <span id="analysis-status">Ready</span>
    </div>
    <script src="../js/code_editor.js"></script>
</body>
</html>
