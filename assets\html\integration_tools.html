<!DOCTYPE html>
<html>
<head>
    <title>Maestro Integration Tools</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="../css/integration_tools.css">
</head>
<body>
    <div class="integration-header">
        <h1 class="integration-title">Integration Tools</h1>
        <div class="status-indicator status-disconnected" id="git-status">
            <i class="fa-solid fa-circle"></i>
            <span>Git: Not Connected</span>
        </div>
    </div>
    
    <div class="integration-container">
        <div class="integration-sidebar">
            <div class="integration-category">
                <div class="category-header">Version Control</div>
                <div class="integration-list">
                    <div class="integration-item active" data-panel="git">
                        <i class="fa-brands fa-git-alt integration-icon"></i>
                        <span>Git Integration</span>
                    </div>
                </div>
            </div>
            
            <div class="integration-category">
                <div class="category-header">Import/Export</div>
                <div class="integration-list">
                    <div class="integration-item" data-panel="export">
                        <i class="fa-solid fa-download integration-icon"></i>
                        <span>Export Project</span>
                    </div>
                    <div class="integration-item" data-panel="import">
                        <i class="fa-solid fa-upload integration-icon"></i>
                        <span>Import Project</span>
                    </div>
                </div>
            </div>
            
            <div class="integration-category">
                <div class="category-header">External Tools</div>
                <div class="integration-list">
                    <div class="integration-item" data-panel="github">
                        <i class="fa-brands fa-github integration-icon"></i>
                        <span>GitHub Integration</span>
                    </div>
                    <div class="integration-item" data-panel="cloud">
                        <i class="fa-solid fa-cloud integration-icon"></i>
                        <span>Cloud Sync</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="integration-main">
            <div class="integration-content">
                <!-- Git Integration Panel -->
                <div class="integration-panel active" id="git">
                    <h2 class="panel-title">Git Integration</h2>
                    <p class="panel-description">
                        Manage your Ring projects with Git version control. Initialize repositories, commit changes, and sync with remote repositories.
                    </p>
                    
                    <div class="git-status">
                        <div class="git-branch">
                            <i class="fa-solid fa-code-branch"></i>
                            <strong>Branch:</strong>
                            <span id="current-branch">main</span>
                        </div>
                        <div class="git-changes">
                            <span id="git-changes-count">0 changes</span> • 
                            <span id="git-last-commit">No commits yet</span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Repository URL</label>
                        <input type="text" class="form-input" id="repo-url" placeholder="https://github.com/username/repository.git">
                    </div>
                    
                    <div class="btn-group">
                        <button class="btn btn-primary" id="git-init-btn">
                            <i class="fa-solid fa-plus"></i> Initialize Repository
                        </button>
                        <button class="btn btn-secondary" id="git-clone-btn">
                            <i class="fa-solid fa-download"></i> Clone Repository
                        </button>
                        <button class="btn btn-success" id="git-commit-btn">
                            <i class="fa-solid fa-check"></i> Commit Changes
                        </button>
                    </div>
                    
                    <div class="form-group" style="margin-top: 20px;">
                        <label class="form-label">Commit Message</label>
                        <textarea class="form-textarea" id="commit-message" placeholder="Enter commit message..."></textarea>
                    </div>
                    
                    <div class="command-output" id="git-output">
Ready for Git commands...
                    </div>
                </div>
                
                <!-- Export Panel -->
                <div class="integration-panel" id="export">
                    <h2 class="panel-title">Export Project</h2>
                    <p class="panel-description">
                        Export your Ring projects in various formats for sharing, backup, or deployment.
                    </p>
                    
                    <div class="export-options">
                        <div class="export-card" data-format="zip">
                            <div class="export-card-icon">
                                <i class="fa-solid fa-file-zipper"></i>
                            </div>
                            <div class="export-card-title">ZIP Archive</div>
                            <div class="export-card-description">Complete project as compressed archive</div>
                        </div>
                        
                        <div class="export-card" data-format="ring">
                            <div class="export-card-icon">
                                <i class="fa-solid fa-file-code"></i>
                            </div>
                            <div class="export-card-title">Ring Package</div>
                            <div class="export-card-description">Optimized Ring application package</div>
                        </div>
                        
                        <div class="export-card" data-format="executable">
                            <div class="export-card-icon">
                                <i class="fa-solid fa-cog"></i>
                            </div>
                            <div class="export-card-title">Executable</div>
                            <div class="export-card-description">Standalone executable file</div>
                        </div>
                        
                        <div class="export-card" data-format="web">
                            <div class="export-card-icon">
                                <i class="fa-solid fa-globe"></i>
                            </div>
                            <div class="export-card-title">Web App</div>
                            <div class="export-card-description">Web-ready application bundle</div>
                        </div>
                    </div>
                    
                    <div class="form-group" style="margin-top: 24px;">
                        <label class="form-label">Export Options</label>
                        <select class="form-select" id="export-options">
                            <option value="full">Full Project</option>
                            <option value="source">Source Code Only</option>
                            <option value="compiled">Compiled Version</option>
                        </select>
                    </div>
                    
                    <div class="btn-group">
                        <button class="btn btn-primary" id="export-btn">
                            <i class="fa-solid fa-download"></i> Export Project
                        </button>
                    </div>
                </div>
                
                <!-- Import Panel -->
                <div class="integration-panel" id="import">
                    <h2 class="panel-title">Import Project</h2>
                    <p class="panel-description">
                        Import Ring projects from various sources and formats.
                    </p>
                    
                    <div class="file-drop-zone" id="drop-zone">
                        <i class="fa-solid fa-cloud-upload-alt" style="font-size: 48px; margin-bottom: 16px; color: var(--accent-primary);"></i>
                        <p><strong>Drop files here</strong> or click to browse</p>
                        <p style="font-size: 12px; color: var(--text-secondary);">
                            Supports: .ring, .zip, .tar.gz files
                        </p>
                        <input type="file" id="file-input" style="display: none;" multiple accept=".ring,.zip,.tar.gz">
                    </div>
                    
                    <div class="form-group" style="margin-top: 24px;">
                        <label class="form-label">Import from URL</label>
                        <input type="text" class="form-input" id="import-url" placeholder="https://example.com/project.zip">
                    </div>
                    
                    <div class="btn-group">
                        <button class="btn btn-primary" id="import-url-btn">
                            <i class="fa-solid fa-link"></i> Import from URL
                        </button>
                        <button class="btn btn-secondary" id="browse-files-btn">
                            <i class="fa-solid fa-folder-open"></i> Browse Files
                        </button>
                    </div>
                </div>
                
                <!-- GitHub Integration Panel -->
                <div class="integration-panel" id="github">
                    <h2 class="panel-title">GitHub Integration</h2>
                    <p class="panel-description">
                        Connect with GitHub to sync your Ring projects and collaborate with others.
                    </p>
                    
                    <div class="form-group">
                        <label class="form-label">GitHub Token</label>
                        <input type="password" class="form-input" id="github-token" placeholder="Enter your GitHub personal access token">
                    </div>
                    
                    <div class="btn-group">
                        <button class="btn btn-primary" id="github-connect-btn">
                            <i class="fa-brands fa-github"></i> Connect to GitHub
                        </button>
                        <button class="btn btn-secondary" id="github-repos-btn">
                            <i class="fa-solid fa-list"></i> List Repositories
                        </button>
                    </div>
                    
                    <div class="command-output" id="github-output" style="margin-top: 16px;">
GitHub integration ready...
                    </div>
                </div>
                
                <!-- Cloud Sync Panel -->
                <div class="integration-panel" id="cloud">
                    <h2 class="panel-title">Cloud Sync</h2>
                    <p class="panel-description">
                        Synchronize your projects with cloud storage services for backup and access across devices.
                    </p>
                    
                    <div class="export-options">
                        <div class="export-card" data-cloud="dropbox">
                            <div class="export-card-icon">
                                <i class="fa-brands fa-dropbox"></i>
                            </div>
                            <div class="export-card-title">Dropbox</div>
                            <div class="export-card-description">Sync with Dropbox storage</div>
                        </div>
                        
                        <div class="export-card" data-cloud="gdrive">
                            <div class="export-card-icon">
                                <i class="fa-brands fa-google-drive"></i>
                            </div>
                            <div class="export-card-title">Google Drive</div>
                            <div class="export-card-description">Sync with Google Drive</div>
                        </div>
                        
                        <div class="export-card" data-cloud="onedrive">
                            <div class="export-card-icon">
                                <i class="fa-brands fa-microsoft"></i>
                            </div>
                            <div class="export-card-title">OneDrive</div>
                            <div class="export-card-description">Sync with Microsoft OneDrive</div>
                        </div>
                    </div>
                    
                    <div class="btn-group" style="margin-top: 24px;">
                        <button class="btn btn-primary" id="cloud-sync-btn">
                            <i class="fa-solid fa-sync"></i> Start Sync
                        </button>
                        <button class="btn btn-secondary" id="cloud-settings-btn">
                            <i class="fa-solid fa-cog"></i> Sync Settings
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="../js/integration_tools.js"></script>
</body>
</html>
