  let currentTool = 'syntax-checker';
        
        // Tool switching functionality
        function switchTool(toolName) {
            // Update sidebar active state
            document.querySelectorAll('.tool-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-tool="${toolName}"]`).classList.add('active');
            
            // Update panel visibility
            document.querySelectorAll('.tool-panel').forEach(panel => {
                panel.classList.remove('active');
            });
            document.getElementById(toolName).classList.add('active');
            
            currentTool = toolName;
        }
        
        // Run analysis
        async function runAnalysis() {
            try {
                // This would integrate with the Ring backend
                const mockAnalysis = {
                    errors: ['Syntax error on line 5: missing semicolon'],
                    warnings: ['Unused variable "temp" on line 12'],
                    suggestions: ['Consider using more descriptive variable names'],
                    metrics: {
                        lines: 45,
                        functions: 3,
                        classes: 1,
                        complexity: 7
                    }
                };
                
                updateAnalysisResults(mockAnalysis);
            } catch (error) {
                console.error('Analysis error:', error);
            }
        }
        
        function updateAnalysisResults(analysis) {
            // Update metrics
            document.getElementById('error-count').textContent = analysis.errors.length;
            document.getElementById('warning-count').textContent = analysis.warnings.length;
            document.getElementById('suggestion-count').textContent = analysis.suggestions.length;
            
            document.getElementById('lines-count').textContent = analysis.metrics.lines;
            document.getElementById('functions-count').textContent = analysis.metrics.functions;
            document.getElementById('classes-count').textContent = analysis.metrics.classes;
            document.getElementById('complexity-score').textContent = analysis.metrics.complexity;
            
            // Update results display
            const resultsContainer = document.getElementById('syntax-results');
            resultsContainer.innerHTML = '';
            
            analysis.errors.forEach(error => {
                const item = document.createElement('div');
                item.className = 'result-item';
                item.innerHTML = `
                    <i class="fa-solid fa-times-circle result-icon error"></i>
                    <span class="result-text">${error}</span>
                `;
                resultsContainer.appendChild(item);
            });
            
            analysis.warnings.forEach(warning => {
                const item = document.createElement('div');
                item.className = 'result-item';
                item.innerHTML = `
                    <i class="fa-solid fa-exclamation-triangle result-icon warning"></i>
                    <span class="result-text">${warning}</span>
                `;
                resultsContainer.appendChild(item);
            });
            
            analysis.suggestions.forEach(suggestion => {
                const item = document.createElement('div');
                item.className = 'result-item';
                item.innerHTML = `
                    <i class="fa-solid fa-lightbulb result-icon success"></i>
                    <span class="result-text">${suggestion}</span>
                `;
                resultsContainer.appendChild(item);
            });
        }
        
        // Console functionality
        function executeConsoleCommand(command) {
            const output = document.getElementById('debug-output');
            output.textContent += `\n> ${command}\n`;
            
            // Mock execution - in real implementation, this would send to Ring interpreter
            if (command.trim()) {
                output.textContent += `Executed: ${command}\n`;
            }
            
            output.scrollTop = output.scrollHeight;
        }
        
        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Tool switching
            document.querySelectorAll('.tool-item').forEach(item => {
                item.addEventListener('click', function() {
                    const toolName = this.getAttribute('data-tool');
                    switchTool(toolName);
                });
            });
            
            // Analysis button
            document.getElementById('run-analysis-btn').addEventListener('click', runAnalysis);
            
            // Console input
            document.getElementById('console-input').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    executeConsoleCommand(this.value);
                    this.value = '';
                }
            });
            
            // Project creation
            document.getElementById('create-project-btn').addEventListener('click', function() {
                const name = document.getElementById('project-name').value;
                const type = document.getElementById('project-type').value;
                
                if (name) {
                    console.log('Creating project:', name, type);
                    // This would integrate with the project creation system
                }
            });
        });