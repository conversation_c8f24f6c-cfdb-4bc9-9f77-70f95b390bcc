 :root {
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --bg-tertiary: #e9ecef;
            --border-color: #dee2e6;
            --text-primary: #212529;
            --text-secondary: #6c757d;
            --accent-primary: #007acc;
            --accent-success: #28a745;
            --accent-warning: #ffc107;
            --accent-danger: #dc3545;
        }
        
        html[data-theme="dark"] {
            --bg-primary: #1e1e1e;
            --bg-secondary: #252526;
            --bg-tertiary: #2d2d30;
            --border-color: #3e3e42;
            --text-primary: #cccccc;
            --text-secondary: #969696;
            --accent-primary: #007acc;
            --accent-success: #4ec9b0;
            --accent-warning: #ffcc02;
            --accent-danger: #f44747;
        }
        
        body {
            margin: 0;
            font-family: 'Inter', sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .integration-header {
            height: 50px;
            background-color: var(--bg-secondary);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }
        
        .integration-title {
            font-size: 16px;
            font-weight: 600;
            margin: 0;
        }
        
        .integration-container {
            flex: 1;
            display: flex;
            overflow: hidden;
        }
        
        .integration-sidebar {
            width: 250px;
            background-color: var(--bg-secondary);
            border-right: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
        }
        
        .integration-category {
            border-bottom: 1px solid var(--border-color);
        }
        
        .category-header {
            padding: 12px 16px;
            background-color: var(--bg-tertiary);
            font-weight: 600;
            font-size: 12px;
            text-transform: uppercase;
            color: var(--text-secondary);
        }
        
        .integration-list {
            padding: 8px;
        }
        
        .integration-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 13px;
        }
        
        .integration-item:hover {
            background-color: var(--bg-tertiary);
        }
        
        .integration-item.active {
            background-color: var(--accent-primary);
            color: white;
        }
        
        .integration-icon {
            font-size: 14px;
            width: 16px;
            text-align: center;
        }
        
        .integration-main {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .integration-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .integration-panel {
            display: none;
        }
        
        .integration-panel.active {
            display: block;
        }
        
        .panel-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
        }
        
        .panel-description {
            color: var(--text-secondary);
            margin-bottom: 24px;
            line-height: 1.6;
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .form-label {
            display: block;
            font-weight: 500;
            margin-bottom: 4px;
            color: var(--text-primary);
        }
        
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            font-size: 14px;
        }
        
        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--accent-primary);
        }
        
        .btn-group {
            display: flex;
            gap: 8px;
            margin-top: 16px;
        }
        
        .btn {
            padding: 8px 16px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }
        
        .btn-primary {
            background-color: var(--accent-primary);
            color: white;
            border-color: var(--accent-primary);
        }
        
        .btn-success {
            background-color: var(--accent-success);
            color: white;
            border-color: var(--accent-success);
        }
        
        .btn-secondary {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
        }
        
        .btn:hover {
            opacity: 0.8;
        }
        
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-connected {
            background-color: rgba(40, 167, 69, 0.1);
            color: var(--accent-success);
        }
        
        .status-disconnected {
            background-color: rgba(220, 53, 69, 0.1);
            color: var(--accent-danger);
        }
        
        .file-drop-zone {
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            color: var(--text-secondary);
            transition: all 0.2s ease;
            cursor: pointer;
        }
        
        .file-drop-zone:hover {
            border-color: var(--accent-primary);
            background-color: rgba(0, 122, 204, 0.05);
        }
        
        .file-drop-zone.dragover {
            border-color: var(--accent-primary);
            background-color: rgba(0, 122, 204, 0.1);
        }
        
        .export-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }
        
        .export-card {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .export-card:hover {
            border-color: var(--accent-primary);
            background-color: var(--bg-secondary);
        }
        
        .export-card-icon {
            font-size: 24px;
            color: var(--accent-primary);
            margin-bottom: 8px;
        }
        
        .export-card-title {
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .export-card-description {
            font-size: 12px;
            color: var(--text-secondary);
        }
        
        .git-status {
            background-color: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
        }
        
        .git-branch {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }
        
        .git-changes {
            font-size: 13px;
            color: var(--text-secondary);
        }
        
        .command-output {
            background-color: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 12px;
            font-family: 'JetBrains Mono', 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }