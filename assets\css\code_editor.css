 :root {
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --bg-tertiary: #e9ecef;
            --border-color: #dee2e6;
            --text-primary: #212529;
            --text-secondary: #6c757d;
            --accent-primary: #007acc;
        }
        
        html[data-theme="dark"] {
            --bg-primary: #1e1e1e;
            --bg-secondary: #252526;
            --bg-tertiary: #2d2d30;
            --border-color: #3e3e42;
            --text-primary: #cccccc;
            --text-secondary: #969696;
            --accent-primary: #007acc;
        }
        
        body {
            margin: 0;
            font-family: 'Inter', sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .editor-toolbar {
            height: 40px;
            background-color: var(--bg-secondary);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            padding: 0 16px;
            gap: 12px;
        }
        
        .toolbar-btn {
            background: none;
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
        }
        
        .toolbar-btn:hover {
            background-color: var(--bg-tertiary);
        }
        
        .toolbar-btn.active {
            background-color: var(--accent-primary);
            color: white;
            border-color: var(--accent-primary);
        }
        
        .editor-container {
            flex: 1;
            display: flex;
        }
        
        .editor-main {
            flex: 1;
            position: relative;
        }
        
        .editor-sidebar {
            width: 250px;
            background-color: var(--bg-secondary);
            border-right: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
        }
        
        .sidebar-section {
            border-bottom: 1px solid var(--border-color);
        }
        
        .sidebar-header {
            padding: 12px 16px;
            background-color: var(--bg-tertiary);
            font-weight: 600;
            font-size: 12px;
            text-transform: uppercase;
            color: var(--text-secondary);
        }
        
        .sidebar-content {
            padding: 8px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .snippet-item {
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 4px;
            transition: all 0.2s ease;
        }
        
        .snippet-item:hover {
            background-color: var(--bg-tertiary);
        }
        
        .snippet-name {
            font-weight: 500;
            font-size: 13px;
            color: var(--text-primary);
        }
        
        .snippet-desc {
            font-size: 11px;
            color: var(--text-secondary);
            margin-top: 2px;
        }
        
        .CodeMirror {
            height: 100%;
            font-family: 'JetBrains Mono', 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .status-bar {
            height: 24px;
            background-color: var(--bg-secondary);
            border-top: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            padding: 0 16px;
            font-size: 11px;
            color: var(--text-secondary);
            gap: 16px;
        }
        
        .analysis-panel {
            height: 150px;
            background-color: var(--bg-secondary);
            border-top: 1px solid var(--border-color);
            display: none;
            flex-direction: column;
        }
        
        .analysis-header {
            height: 32px;
            background-color: var(--bg-tertiary);
            display: flex;
            align-items: center;
            padding: 0 16px;
            font-weight: 600;
            font-size: 12px;
        }
        
        .analysis-content {
            flex: 1;
            padding: 8px 16px;
            overflow-y: auto;
            font-size: 12px;
        }
        
        .error-item, .warning-item, .suggestion-item {
            padding: 4px 8px;
            border-radius: 4px;
            margin-bottom: 4px;
        }
        
        .error-item {
            background-color: rgba(220, 53, 69, 0.1);
            border-left: 3px solid #dc3545;
        }
        
        .warning-item {
            background-color: rgba(255, 193, 7, 0.1);
            border-left: 3px solid #ffc107;
        }
        
        .suggestion-item {
            background-color: rgba(0, 123, 255, 0.1);
            border-left: 3px solid #007bff;
        }