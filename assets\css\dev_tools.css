 :root {
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --bg-tertiary: #e9ecef;
            --border-color: #dee2e6;
            --text-primary: #212529;
            --text-secondary: #6c757d;
            --text-muted: #adb5bd;
            --accent-primary: #007acc;
            --accent-success: #28a745;
            --accent-warning: #ffc107;
            --accent-danger: #dc3545;
            --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        
        html[data-theme="dark"] {
            --bg-primary: #1e1e1e;
            --bg-secondary: #252526;
            --bg-tertiary: #2d2d30;
            --border-color: #3e3e42;
            --text-primary: #cccccc;
            --text-secondary: #969696;
            --text-muted: #6a6a6a;
            --accent-primary: #007acc;
            --accent-success: #4ec9b0;
            --accent-warning: #ffcc02;
            --accent-danger: #f44747;
        }
        
        body {
            margin: 0;
            font-family: 'Inter', sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .tools-header {
            height: 50px;
            background-color: var(--bg-secondary);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }
        
        .tools-title {
            font-size: 16px;
            font-weight: 600;
            margin: 0;
        }
        
        .tools-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            background: none;
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
        }
        
        .action-btn:hover {
            background-color: var(--bg-tertiary);
        }
        
        .action-btn.primary {
            background-color: var(--accent-primary);
            color: white;
            border-color: var(--accent-primary);
        }
        
        .tools-container {
            flex: 1;
            display: flex;
            overflow: hidden;
        }
        
        .tools-sidebar {
            width: 250px;
            background-color: var(--bg-secondary);
            border-right: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
        }
        
        .tool-category {
            border-bottom: 1px solid var(--border-color);
        }
        
        .category-header {
            padding: 12px 16px;
            background-color: var(--bg-tertiary);
            font-weight: 600;
            font-size: 12px;
            text-transform: uppercase;
            color: var(--text-secondary);
        }
        
        .tool-list {
            padding: 8px;
        }
        
        .tool-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 13px;
        }
        
        .tool-item:hover {
            background-color: var(--bg-tertiary);
        }
        
        .tool-item.active {
            background-color: var(--accent-primary);
            color: white;
        }
        
        .tool-icon {
            font-size: 14px;
            width: 16px;
            text-align: center;
        }
        
        .tools-main {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .tool-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .tool-panel {
            display: none;
        }
        
        .tool-panel.active {
            display: block;
        }
        
        .panel-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
        }
        
        .panel-description {
            color: var(--text-secondary);
            margin-bottom: 24px;
            line-height: 1.6;
        }
        
        .analysis-results {
            background-color: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
        }
        
        .result-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        .result-item:last-child {
            border-bottom: none;
        }
        
        .result-icon {
            font-size: 14px;
            width: 16px;
        }
        
        .result-icon.error {
            color: var(--accent-danger);
        }
        
        .result-icon.warning {
            color: var(--accent-warning);
        }
        
        .result-icon.success {
            color: var(--accent-success);
        }
        
        .result-text {
            flex: 1;
            font-size: 13px;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .metric-card {
            background-color: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 16px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: 700;
            color: var(--accent-primary);
            margin-bottom: 4px;
        }
        
        .metric-label {
            font-size: 12px;
            color: var(--text-secondary);
            text-transform: uppercase;
        }
        
        .debug-console {
            background-color: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            height: 200px;
            font-family: 'JetBrains Mono', 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            padding: 12px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .performance-chart {
            background-color: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .form-label {
            display: block;
            font-weight: 500;
            margin-bottom: 4px;
            color: var(--text-primary);
        }
        
        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            font-size: 14px;
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--accent-primary);
        }
        
        .btn-group {
            display: flex;
            gap: 8px;
            margin-top: 16px;
        }
        
        .btn {
            padding: 8px 16px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background-color: var(--accent-primary);
            color: white;
            border-color: var(--accent-primary);
        }
        
        .btn-secondary {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
        }
        
        .btn:hover {
            opacity: 0.8;
        }