      let editor;
        let currentTheme = 'light';
        let analysisVisible = false;
        
        // Initialize CodeMirror editor
        function initializeEditor() {
            editor = CodeMirror.fromTextArea(document.getElementById('code-editor'), {
                mode: 'javascript', // We'll use JavaScript mode as base for Ring
                theme: 'eclipse',
                lineNumbers: true,
                autoCloseBrackets: true,
                matchBrackets: true,
                indentUnit: 4,
                indentWithTabs: false,
                foldGutter: true,
                gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter"],
                extraKeys: {
                    "Ctrl-Space": "autocomplete",
                    "Ctrl-S": saveFile,
                    "Ctrl-F": formatCode,
                    "F5": runCode
                }
            });
            
            // Update cursor position
            editor.on('cursorActivity', function() {
                const cursor = editor.getCursor();
                document.getElementById('cursor-position').textContent = 
                    `Line ${cursor.line + 1}, Column ${cursor.ch + 1}`;
            });
            
            // Auto-save on change
            editor.on('change', function() {
                // Debounced auto-save could be implemented here
            });
        }
        
        // Load code snippets
        async function loadCodeSnippets() {
            try {
                let snippets;

                if (window.getCodeSnippets) {
                    snippets = await window.getCodeSnippets();
                    if (typeof snippets === 'string') {
                        snippets = JSON.parse(snippets);
                    }
                } else {
                    // Fallback snippets
                    snippets = getDefaultSnippets();
                }

                const snippetsList = document.getElementById('snippets-list');
                snippetsList.innerHTML = '';

                snippets.forEach(snippet => {
                    const item = document.createElement('div');
                    item.className = 'snippet-item';
                    item.innerHTML = `
                        <div class="snippet-name">${snippet.name}</div>
                        <div class="snippet-desc">${snippet.description}</div>
                    `;
                    item.onclick = () => insertSnippet(snippet.code);
                    snippetsList.appendChild(item);
                });
            } catch (error) {
                console.error('Error loading snippets:', error);
                // Load fallback snippets
                loadFallbackSnippets();
            }
        }

        function getDefaultSnippets() {
            return [
                {
                    name: "Hello World",
                    description: "Basic Hello World program",
                    code: 'see "Hello, World!" + nl'
                },
                {
                    name: "Function Template",
                    description: "Basic function template",
                    code: 'func functionName(param1, param2)\n    # Your code here\n    return result'
                },
                {
                    name: "Class Template",
                    description: "Basic class template",
                    code: 'class ClassName\n    # Properties\n    cName = ""\n    \n    func init()\n        # Constructor\n    \n    func methodName()\n        # Method implementation'
                }
            ];
        }

        function loadFallbackSnippets() {
            const snippets = getDefaultSnippets();
            const snippetsList = document.getElementById('snippets-list');
            snippetsList.innerHTML = '';

            snippets.forEach(snippet => {
                const item = document.createElement('div');
                item.className = 'snippet-item';
                item.innerHTML = `
                    <div class="snippet-name">${snippet.name}</div>
                    <div class="snippet-desc">${snippet.description}</div>
                `;
                item.onclick = () => insertSnippet(snippet.code);
                snippetsList.appendChild(item);
            });
        }
        
        function insertSnippet(code) {
            const cursor = editor.getCursor();
            editor.replaceRange(code, cursor);
            editor.focus();
        }
        
        async function saveFile() {
            // Implementation for saving file
            console.log('Saving file...');
        }
        
        async function formatCode() {
            try {
                const code = editor.getValue();
                let formattedCode;

                if (window.formatCode) {
                    formattedCode = await window.formatCode(code);
                    if (typeof formattedCode === 'string' && formattedCode.startsWith('"') && formattedCode.endsWith('"')) {
                        formattedCode = formattedCode.slice(1, -1); // Remove quotes
                    }
                } else {
                    // Fallback formatting
                    formattedCode = performBasicFormatting(code);
                }

                editor.setValue(formattedCode);
                document.getElementById('analysis-status').textContent = 'Code formatted';
            } catch (error) {
                console.error('Error formatting code:', error);
                document.getElementById('analysis-status').textContent = 'Formatting failed';
            }
        }

        function performBasicFormatting(code) {
            const lines = code.split('\n');
            const formattedLines = [];
            let indentLevel = 0;

            lines.forEach(line => {
                const trimmedLine = line.trim();

                // Decrease indent for closing braces
                if (trimmedLine === '}' || trimmedLine === 'ok' || trimmedLine === 'next' || trimmedLine === 'done') {
                    indentLevel = Math.max(0, indentLevel - 1);
                }

                // Add indentation
                const indent = '    '.repeat(indentLevel);
                formattedLines.push(indent + trimmedLine);

                // Increase indent for opening braces and control structures
                if (trimmedLine.endsWith('{') ||
                    trimmedLine.startsWith('if ') ||
                    trimmedLine.startsWith('for ') ||
                    trimmedLine.startsWith('while ') ||
                    trimmedLine.startsWith('func ') ||
                    trimmedLine.startsWith('class ') ||
                    trimmedLine === 'try') {
                    indentLevel++;
                }
            });

            return formattedLines.join('\n');
        }
        
        async function analyzeCode() {
            // Prevent multiple simultaneous calls
            if (analyzeCode.isRunning) {
                return;
            }
            analyzeCode.isRunning = true;

            try {
                const code = editor.getValue();
                let analysis;

                // Always use fallback analysis for now to avoid errors
                analysis = performBasicAnalysis(code);

                // Try backend analysis if available (but don't fail if it doesn't work)
                try {
                    if (window.analyzeCode && typeof window.analyzeCode === 'function') {
                        const backendAnalysis = await window.analyzeCode(code);
                        if (backendAnalysis && typeof backendAnalysis === 'string') {
                            const parsed = JSON.parse(backendAnalysis);
                            if (parsed && parsed.errors) {
                                analysis = parsed;
                            }
                        } else if (backendAnalysis && backendAnalysis.errors) {
                            analysis = backendAnalysis;
                        }
                    }
                } catch (backendError) {
                    console.log('Backend analysis not available, using fallback');
                }

                displayAnalysis(analysis);
                document.getElementById('analysis-status').textContent =
                    `Analysis: ${analysis.errors ? analysis.errors.length : 0} errors, ${analysis.warnings ? analysis.warnings.length : 0} warnings`;
            } catch (error) {
                console.error('Error analyzing code:', error);
                // Show safe fallback analysis
                const fallbackAnalysis = {
                    errors: [],
                    warnings: [],
                    suggestions: ['Code analysis temporarily unavailable'],
                    metrics: { lines: 0, functions: 0, classes: 0, complexity: 0 }
                };
                displayAnalysis(fallbackAnalysis);
                document.getElementById('analysis-status').textContent = 'Analysis failed';
            } finally {
                analyzeCode.isRunning = false;
            }
        }

        function performBasicAnalysis(code) {
            const lines = code.split('\n');
            const analysis = {
                errors: [],
                warnings: [],
                suggestions: [],
                metrics: {
                    lines: lines.length,
                    functions: 0,
                    classes: 0,
                    complexity: 0
                }
            };

            // Basic analysis
            let openBraces = 0;
            let closeBraces = 0;

            lines.forEach((line, index) => {
                const trimmedLine = line.trim();

                // Count functions and classes
                if (trimmedLine.startsWith('func ')) {
                    analysis.metrics.functions++;
                }
                if (trimmedLine.startsWith('class ')) {
                    analysis.metrics.classes++;
                }

                // Count braces
                openBraces += (line.match(/\{/g) || []).length;
                closeBraces += (line.match(/\}/g) || []).length;
            });

            // Check for mismatched braces
            if (openBraces !== closeBraces) {
                analysis.errors.push(`Mismatched braces: ${openBraces} opening, ${closeBraces} closing`);
            }

            // Add suggestions
            if (analysis.metrics.functions === 0 && lines.length > 5) {
                analysis.suggestions.push('Consider organizing code into functions for better structure');
            }

            return analysis;
        }
        
        function displayAnalysis(analysis) {
            const content = document.getElementById('analysis-content');
            content.innerHTML = '';

            // Ensure analysis object has required properties
            const safeAnalysis = {
                errors: analysis.errors || [],
                warnings: analysis.warnings || [],
                suggestions: analysis.suggestions || [],
                metrics: analysis.metrics || { lines: 0, functions: 0, classes: 0, complexity: 0 }
            };

            // Display errors
            safeAnalysis.errors.forEach(error => {
                const item = document.createElement('div');
                item.className = 'error-item';
                item.textContent = `Error: ${error}`;
                content.appendChild(item);
            });

            // Display warnings
            safeAnalysis.warnings.forEach(warning => {
                const item = document.createElement('div');
                item.className = 'warning-item';
                item.textContent = `Warning: ${warning}`;
                content.appendChild(item);
            });

            // Display suggestions
            safeAnalysis.suggestions.forEach(suggestion => {
                const item = document.createElement('div');
                item.className = 'suggestion-item';
                item.textContent = `Suggestion: ${suggestion}`;
                content.appendChild(item);
            });

            // Show metrics
            const metrics = document.createElement('div');
            metrics.innerHTML = `
                <strong>Metrics:</strong><br>
                Lines: ${safeAnalysis.metrics.lines}<br>
                Functions: ${safeAnalysis.metrics.functions}<br>
                Classes: ${safeAnalysis.metrics.classes}<br>
                Complexity: ${safeAnalysis.metrics.complexity || 0}
            `;
            content.appendChild(metrics);

            // Show success message if no issues
            if (safeAnalysis.errors.length === 0 && safeAnalysis.warnings.length === 0) {
                const successItem = document.createElement('div');
                successItem.className = 'suggestion-item';
                successItem.innerHTML = '<i class="fa-solid fa-check-circle"></i> No issues found in your code!';
                content.appendChild(successItem);
            }
        }
        
        function runCode() {
            // Implementation for running Ring code
            console.log('Running code...');
            document.getElementById('analysis-status').textContent = 'Running code...';
        }
        
        function toggleAnalysis() {
            analysisVisible = !analysisVisible;
            const panel = document.getElementById('analysis-panel');
            panel.style.display = analysisVisible ? 'flex' : 'none';
            
            if (analysisVisible) {
                analyzeCode();
            }
        }
        
        function toggleTheme() {
            currentTheme = currentTheme === 'light' ? 'dark' : 'light';
            document.documentElement.setAttribute('data-theme', currentTheme);
            editor.setOption('theme', currentTheme === 'dark' ? 'monokai' : 'eclipse');
            
            const btn = document.getElementById('theme-toggle-btn');
            btn.textContent = currentTheme === 'dark' ? 'Light Theme' : 'Dark Theme';
        }
        
        // Event listeners
        document.getElementById('new-file-btn').addEventListener('click', () => {
            editor.setValue('# New Ring file\n\n');
            editor.focus();
        });
        
        document.getElementById('save-file-btn').addEventListener('click', saveFile);
        document.getElementById('format-code-btn').addEventListener('click', formatCode);
        document.getElementById('analyze-code-btn').addEventListener('click', analyzeCode);
        document.getElementById('run-code-btn').addEventListener('click', runCode);
        document.getElementById('toggle-analysis-btn').addEventListener('click', toggleAnalysis);
        document.getElementById('theme-toggle-btn').addEventListener('click', toggleTheme);
        
        // Initialize when page loads
        window.onload = function() {
            initializeEditor();
            loadCodeSnippets();
            
            // Set initial content
            editor.setValue(`# Welcome to Maestro Ring Code Editor
# This is an advanced IDE for Ring programming language

func main()
    see "Hello from Maestro Assistant!" + nl

# Example function
func calculateSum(nNum1, nNum2)
    return nNum1 + nNum2

# Example class
class Person
    cName = ""
    nAge = 0
    
    func init(cName, nAge)
        this.cName = cName
        this.nAge = nAge
    
    func introduce()
        see "Hello, I'm " + cName + " and I'm " + nAge + " years old." + nl
`);
        };