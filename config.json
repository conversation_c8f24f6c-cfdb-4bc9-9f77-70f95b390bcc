{"application": {"name": "Maestro Assistant", "version": "3.0", "description": "Advanced Ring Programming IDE with AI Integration", "author": "Maestro Development Team"}, "ui": {"defaultTheme": "light", "defaultLanguage": "en", "fontSize": 14, "fontFamily": "Inter, sans-serif", "codeFont": "JetBrains Mono, Consolas, Monaco, monospace", "autoSave": true, "autoSaveInterval": 30000}, "editor": {"tabSize": 4, "insertSpaces": true, "wordWrap": true, "lineNumbers": true, "minimap": true, "bracketMatching": true, "autoCloseBrackets": true, "syntaxHighlighting": true, "codeCompletion": true, "errorHighlighting": true}, "ai": {"provider": "gemini", "model": "gemini-pro", "maxTokens": 2048, "temperature": 0.7, "cacheResponses": true, "cacheTimeout": 3600000, "enhancedPrompts": true, "contextAware": true}, "features": {"codeAnalysis": true, "syntaxChecking": true, "performanceProfiler": true, "debugConsole": true, "gitIntegration": true, "cloudSync": false, "exportImport": true, "projectTemplates": true}, "performance": {"maxHistoryItems": 100, "maxCacheItems": 50, "memoryOptimization": true, "lazyLoading": true, "asyncOperations": true}, "security": {"validateInputs": true, "sanitizeOutputs": true, "secureApiCalls": true, "encryptSettings": false}, "integrations": {"git": {"enabled": true, "autoCommit": false, "defaultBranch": "main"}, "github": {"enabled": false, "token": "", "defaultRepo": ""}, "cloud": {"enabled": false, "provider": "", "syncInterval": 300000}}, "shortcuts": {"newFile": "Ctrl+N", "saveFile": "Ctrl+S", "formatCode": "Ctrl+Shift+F", "runCode": "F5", "toggleTheme": "Ctrl+Shift+T", "toggleLanguage": "Ctrl+Shift+L", "showCommands": "Ctrl+Shift+P", "toggleSidebar": "Ctrl+B", "toggleTerminal": "Ctrl+`"}, "templates": [{"name": "Console Application", "description": "Basic console application template", "type": "console", "files": [{"name": "main.ring", "content": "# Console Application\n# Author: Your Name\n# Date: {date}\n\nfunc main()\n    see \"Hello from Ring Console App!\" + nl\n\nmain()"}]}, {"name": "GUI Application", "description": "Basic GUI application template", "type": "gui", "files": [{"name": "main.ring", "content": "# GUI Application\n# Author: Your Name\n# Date: {date}\n\nload \"guilib.ring\"\n\nfunc main()\n    new qApp {\n        new qWidget() {\n            setWindowTitle(\"Ring GUI App\")\n            resize(400, 300)\n            show()\n        }\n        exec()\n    }\n\nmain()"}]}, {"name": "Web Application", "description": "Basic web application template", "type": "web", "files": [{"name": "main.ring", "content": "# Web Application\n# Author: Your Name\n# Date: {date}\n\nload \"weblib.ring\"\n\nfunc main()\n    import System.Web\n    \n    new WebPage {\n        text(\"<h1>Welcome to Ring Web App</h1>\")\n    }\n\nmain()"}]}, {"name": "Library", "description": "Basic library template", "type": "library", "files": [{"name": "lib.ring", "content": "# Ring Library\n# Author: Your Name\n# Date: {date}\n\npackage MyLibrary\n\nclass MyClass\n    # Properties\n    cName = \"\"\n    \n    # Methods\n    func init(cName)\n        this.cName = cName\n    \n    func getName()\n        return cName\n    \n    func setName(cNewName)\n        cName = cNewName"}]}], "codeSnippets": [{"name": "Function Template", "prefix": "func", "description": "Create a new function", "body": "func ${1:functionName}(${2:parameters})\n    ${3:// Function body}\n    return ${4:result}"}, {"name": "Class Template", "prefix": "class", "description": "Create a new class", "body": "class ${1:ClassName}\n    # Properties\n    ${2:property} = ${3:value}\n    \n    # Constructor\n    func init(${4:parameters})\n        ${5:// Initialize properties}\n    \n    # Methods\n    func ${6:methodName}()\n        ${7:// Method body}"}, {"name": "If Statement", "prefix": "if", "description": "If-else statement", "body": "if ${1:condition}\n    ${2:// Code when true}\nelse\n    ${3:// Code when false}\nok"}, {"name": "For Loop", "prefix": "for", "description": "For loop", "body": "for ${1:i} = ${2:1} to ${3:10}\n    ${4:// Loop body}\nnext"}, {"name": "While Loop", "prefix": "while", "description": "While loop", "body": "while ${1:condition}\n    ${2:// Loop body}\nend"}, {"name": "Try-Catch", "prefix": "try", "description": "Error handling", "body": "try\n    ${1:// Code that might throw an error}\ncatch\n    ${2:// Error handling}\n    see \"Error: \" + cCatchError + nl\ndone"}], "documentation": {"autoUpdate": true, "showExamples": true, "interactiveMode": true, "searchEnabled": true, "offlineMode": false}, "debugging": {"enableBreakpoints": true, "showVariables": true, "stepThroughCode": true, "watchExpressions": true, "callStack": true}, "export": {"defaultFormat": "zip", "includeDocumentation": true, "compressOutput": true, "createReadme": true}, "import": {"supportedFormats": ["ring", "zip", "tar.gz"], "autoDetectFormat": true, "validateStructure": true, "backupOriginal": true}}