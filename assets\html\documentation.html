<!DOCTYPE html>
<html>
<head>
    <title>Ring Programming Documentation</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-dark.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <link rel="stylesheet" href="../css/documentation.css">
</head>
<body>
    <div class="docs-sidebar">
        <div class="docs-header">
            <h1 class="docs-title">Ring Documentation</h1>
            <p class="docs-subtitle">Interactive Programming Guide</p>
        </div>
        
        <div class="search-box">
            <input type="text" class="search-input" placeholder="Search documentation..." id="search-input">
        </div>
        
        <div class="docs-nav" id="docs-nav">
            <!-- Navigation will be populated by JavaScript -->
        </div>
    </div>
    
    <div class="docs-content">
        <div class="content-header">
            <h2 class="content-title" id="content-title">Welcome to Ring Documentation</h2>
            <div class="content-actions">
                <button class="action-btn" id="quick-ref-btn">Quick Reference</button>
                <button class="action-btn" id="theme-toggle-btn">Dark Theme</button>
            </div>
        </div>
        
        <div class="content-main" id="content-main">
            <div class="welcome-content" id="welcome-content">
                <div class="welcome-icon">
                    <i class="fa-solid fa-book-open"></i>
                </div>
                <h3 class="welcome-title">Ring Programming Language</h3>
                <p class="welcome-description">
                    Explore comprehensive documentation for the Ring programming language. 
                    Learn from basic concepts to advanced topics with interactive examples 
                    and practical code snippets.
                </p>
            </div>
            
            <div class="topic-content" id="topic-content" style="display: none;">
                <!-- Topic content will be populated here -->
            </div>
            
            <div class="quick-reference" id="quick-reference">
                <div class="reference-grid">
                    <div class="reference-card">
                        <div class="reference-card-header">Keywords</div>
                        <div class="reference-card-content" id="keywords-list">
                            <!-- Keywords will be populated here -->
                        </div>
                    </div>
                    <div class="reference-card">
                        <div class="reference-card-header">Operators</div>
                        <div class="reference-card-content" id="operators-list">
                            <!-- Operators will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="../js/documentation.js"></script>
</body>
</html>
