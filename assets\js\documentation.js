let documentation = {};
        let currentTheme = 'light';
        let currentLanguage = 'en';
        
        // Load documentation data
        async function loadDocumentation() {
            try {
                // Use Ring backend to load documentation instead of direct file access
                if (window.getDocumentation) {
                    const docData = await window.getDocumentation();
                    documentation = JSON.parse(docData);
                } else {
                    // Fallback to embedded documentation
                    documentation = getEmbeddedDocumentation();
                }
                renderNavigation();
                renderQuickReference();
            } catch (error) {
                console.error('Error loading documentation:', error);
                // Use fallback documentation
                documentation = getEmbeddedDocumentation();
                renderNavigation();
                renderQuickReference();
            }
        }

        function getEmbeddedDocumentation() {
            return {
                "categories": [
                    {
                        "name": "Basic Concepts",
                        "name_ar": "المفاهيم الأساسية",
                        "topics": [
                            {
                                "title": "Variables and Data Types",
                                "title_ar": "المتغيرات وأنواع البيانات",
                                "content": "Ring supports dynamic typing. Variables can hold strings, numbers, lists, and objects.",
                                "content_ar": "تدعم Ring الكتابة الديناميكية. يمكن للمتغيرات أن تحتوي على نصوص وأرقام وقوائم وكائنات.",
                                "examples": [
                                    {
                                        "title": "String Variables",
                                        "code": "cName = \"Ahmed\"\ncMessage = 'Hello World'\nsee cName + \": \" + cMessage + nl"
                                    }
                                ]
                            }
                        ]
                    }
                ],
                "quick_reference": {
                    "keywords": [
                        {"keyword": "func", "description": "Define a function", "description_ar": "تعريف دالة"},
                        {"keyword": "class", "description": "Define a class", "description_ar": "تعريف كلاس"}
                    ],
                    "operators": [
                        {"operator": "+", "description": "Addition", "description_ar": "الجمع"},
                        {"operator": "=", "description": "Assignment", "description_ar": "الإسناد"}
                    ]
                }
            };
        }
        
        function renderNavigation() {
            const nav = document.getElementById('docs-nav');
            nav.innerHTML = '';
            
            documentation.categories.forEach((category, categoryIndex) => {
                const categoryDiv = document.createElement('div');
                categoryDiv.className = 'nav-category';
                
                const header = document.createElement('div');
                header.className = 'category-header';
                header.innerHTML = `
                    <i class="fa-solid fa-chevron-down category-icon"></i>
                    ${currentLanguage === 'ar' ? category.name_ar : category.name}
                `;
                
                const topics = document.createElement('div');
                topics.className = 'category-topics';
                
                category.topics.forEach((topic, topicIndex) => {
                    const topicDiv = document.createElement('div');
                    topicDiv.className = 'topic-item';
                    topicDiv.textContent = currentLanguage === 'ar' ? topic.title_ar : topic.title;
                    topicDiv.onclick = () => showTopic(categoryIndex, topicIndex);
                    topics.appendChild(topicDiv);
                });
                
                header.onclick = () => toggleCategory(header, topics);
                
                categoryDiv.appendChild(header);
                categoryDiv.appendChild(topics);
                nav.appendChild(categoryDiv);
            });
        }
        
        function toggleCategory(header, topics) {
            header.classList.toggle('collapsed');
            topics.style.display = topics.style.display === 'none' ? 'block' : 'none';
        }
        
        function showTopic(categoryIndex, topicIndex) {
            const topic = documentation.categories[categoryIndex].topics[topicIndex];
            
            // Update active state
            document.querySelectorAll('.topic-item').forEach(item => item.classList.remove('active'));
            event.target.classList.add('active');
            
            // Update content
            document.getElementById('content-title').textContent = 
                currentLanguage === 'ar' ? topic.title_ar : topic.title;
            
            const topicContent = document.getElementById('topic-content');
            topicContent.innerHTML = `
                <div class="topic-description">
                    ${currentLanguage === 'ar' ? topic.content_ar : topic.content}
                </div>
                
                <div class="examples-section">
                    <h3 class="section-title">Examples</h3>
                    ${topic.examples.map(example => `
                        <div class="example-item">
                            <div class="example-header">
                                <span class="example-title">${example.title}</span>
                                <div class="example-actions">
                                    <button class="example-btn" onclick="copyCode(this)">
                                        <i class="fa-solid fa-copy"></i> Copy
                                    </button>
                                    <button class="example-btn" onclick="runCode(this)">
                                        <i class="fa-solid fa-play"></i> Run
                                    </button>
                                </div>
                            </div>
                            <div class="example-code">
                                <pre><code class="language-ring">${example.code}</code></pre>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
            
            // Show topic content
            document.getElementById('welcome-content').style.display = 'none';
            document.getElementById('quick-reference').classList.remove('active');
            topicContent.style.display = 'block';
            
            // Highlight code
            Prism.highlightAll();
        }
        
        function renderQuickReference() {
            const keywordsList = document.getElementById('keywords-list');
            const operatorsList = document.getElementById('operators-list');
            
            keywordsList.innerHTML = documentation.quick_reference.keywords.map(item => `
                <div class="reference-item">
                    <span class="reference-keyword">${item.keyword}</span>
                    <span class="reference-description">
                        ${currentLanguage === 'ar' ? item.description_ar : item.description}
                    </span>
                </div>
            `).join('');
            
            operatorsList.innerHTML = documentation.quick_reference.operators.map(item => `
                <div class="reference-item">
                    <span class="reference-keyword">${item.operator}</span>
                    <span class="reference-description">
                        ${currentLanguage === 'ar' ? item.description_ar : item.description}
                    </span>
                </div>
            `).join('');
        }
        
        function showQuickReference() {
            document.getElementById('content-title').textContent = 'Quick Reference';
            document.getElementById('welcome-content').style.display = 'none';
            document.getElementById('topic-content').style.display = 'none';
            document.getElementById('quick-reference').classList.add('active');
            
            // Remove active state from topics
            document.querySelectorAll('.topic-item').forEach(item => item.classList.remove('active'));
        }
        
        function copyCode(button) {
            const code = button.closest('.example-item').querySelector('code').textContent;
            navigator.clipboard.writeText(code).then(() => {
                button.innerHTML = '<i class="fa-solid fa-check"></i> Copied!';
                setTimeout(() => {
                    button.innerHTML = '<i class="fa-solid fa-copy"></i> Copy';
                }, 2000);
            });
        }
        
        function runCode(button) {
            const code = button.closest('.example-item').querySelector('code').textContent;
            // Here you would integrate with the Ring interpreter
            console.log('Running code:', code);
            button.innerHTML = '<i class="fa-solid fa-check"></i> Running...';
            setTimeout(() => {
                button.innerHTML = '<i class="fa-solid fa-play"></i> Run';
            }, 2000);
        }
        
        function toggleTheme() {
            currentTheme = currentTheme === 'light' ? 'dark' : 'light';
            document.documentElement.setAttribute('data-theme', currentTheme);
            
            const btn = document.getElementById('theme-toggle-btn');
            btn.textContent = currentTheme === 'dark' ? 'Light Theme' : 'Dark Theme';
        }
        
        function searchDocumentation(query) {
            // Simple search implementation
            const items = document.querySelectorAll('.topic-item');
            items.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (text.includes(query.toLowerCase())) {
                    item.style.display = 'block';
                    item.parentElement.style.display = 'block';
                } else {
                    item.style.display = query ? 'none' : 'block';
                }
            });
        }
        
        // Event listeners
        document.getElementById('quick-ref-btn').addEventListener('click', showQuickReference);
        document.getElementById('theme-toggle-btn').addEventListener('click', toggleTheme);
        document.getElementById('search-input').addEventListener('input', (e) => {
            searchDocumentation(e.target.value);
        });
        
        // Initialize
        window.onload = function() {
            loadDocumentation();
        };