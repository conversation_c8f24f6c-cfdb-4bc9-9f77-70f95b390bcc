let currentTheme, currentLang, currentPanel = 'chat';
        const uiStrings = {
            en: {
                title: "Maestro Assistant",
                placeholder: "Ask me anything about Ring programming...",
                newChat: "New Chat",
                copy: "Copy",
                copied: "Copied!",
                chatAssistant: "Chat Assistant",
                codeEditor: "Code Editor",
                projects: "Projects",
                documentation: "Documentation",
                settings: "Settings"
            },
            ar: {
                title: "مساعد مايسترو",
                placeholder: "اسألني أي شيء عن برمجة Ring...",
                newChat: "محادثة جديدة",
                copy: "نسخ",
                copied: "تم النسخ!",
                chatAssistant: "مساعد الدردشة",
                codeEditor: "محرر الكود",
                projects: "المشاريع",
                documentation: "التوثيق",
                settings: "الإعدادات"
            }
        };

        // Sidebar functionality
        function initializeSidebar() {
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const sidebarItems = document.querySelectorAll('.sidebar-item');

            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('expanded');
            });

            sidebarItems.forEach(item => {
                item.addEventListener('click', () => {
                    const panel = item.dataset.panel;
                    switchPanel(panel);

                    // Update active state
                    sidebarItems.forEach(i => i.classList.remove('active'));
                    item.classList.add('active');
                });
            });
        }

        function switchPanel(panel) {
            currentPanel = panel;

            // Hide all panels
            document.getElementById('chat-panel').style.display = 'none';
            document.getElementById('editor-panel').style.display = 'none';
            document.getElementById('docs-panel').style.display = 'none';
            document.getElementById('projects-panel').style.display = 'none';
            document.getElementById('devtools-panel').style.display = 'none';
            document.getElementById('integration-panel').style.display = 'none';
            document.getElementById('settings-panel').style.display = 'none';

            // Show selected panel
            switch(panel) {
                case 'chat':
                    document.getElementById('chat-panel').style.display = 'flex';
                    break;
                case 'editor':
                    document.getElementById('editor-panel').style.display = 'block';
                    break;
                case 'docs':
                    document.getElementById('docs-panel').style.display = 'block';
                    break;
                case 'projects':
                    document.getElementById('projects-panel').style.display = 'block';
                    loadProjects();
                    break;
                case 'devtools':
                    document.getElementById('devtools-panel').style.display = 'block';
                    break;
                case 'integration':
                    document.getElementById('integration-panel').style.display = 'block';
                    break;
                case 'settings':
                    document.getElementById('settings-panel').style.display = 'block';
                    loadSettings();
                    break;
            }

            // Update editor tab
            const editorTabs = document.querySelectorAll('.editor-tab');
            editorTabs.forEach(tab => tab.classList.remove('active'));

            const activeTab = document.querySelector(`[data-tab="${panel}"]`);
            if (activeTab) {
                activeTab.classList.add('active');
            } else {
                // Create new tab if it doesn't exist
                createEditorTab(panel);
            }
        }

        function createEditorTab(panel) {
            const tabsContainer = document.querySelector('.editor-tabs');
            const newTab = document.createElement('div');
            newTab.className = 'editor-tab active';
            newTab.setAttribute('data-tab', panel);

            const panelNames = {
                'chat': { icon: 'fa-comments', name: 'AI Assistant' },
                'editor': { icon: 'fa-code', name: 'Code Editor' },
                'docs': { icon: 'fa-book', name: 'Documentation' },
                'projects': { icon: 'fa-folder', name: 'Projects' },
                'devtools': { icon: 'fa-tools', name: 'Dev Tools' },
                'integration': { icon: 'fa-plug', name: 'Integration' },
                'settings': { icon: 'fa-cog', name: 'Settings' }
            };

            const panelInfo = panelNames[panel] || { icon: 'fa-file', name: panel };

            newTab.innerHTML = `
                <i class="fa-solid ${panelInfo.icon}"></i>
                <span>${panelInfo.name}</span>
                <button class="close-btn" onclick="closeTab('${panel}')">×</button>
            `;

            tabsContainer.appendChild(newTab);
        }

        function closeTab(panel) {
            const tab = document.querySelector(`[data-tab="${panel}"]`);
            if (tab && panel !== 'chat') { // Don't allow closing the main chat tab
                tab.remove();
                switchPanel('chat'); // Switch back to chat
            }
        }

        async function loadProjects() {
            try {
                const projects = await window.getProjects();
                const projectsList = document.getElementById('projects-list');

                if (projects.length === 0) {
                    projectsList.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: var(--text-secondary);">
                            <i class="fa-solid fa-folder-open" style="font-size: 48px; margin-bottom: 16px;"></i>
                            <p>No projects yet. Create your first Ring project!</p>
                        </div>
                    `;
                    return;
                }

                projectsList.innerHTML = projects.map(project => `
                    <div class="project-card" onclick="openProject('${project.name}')">
                        <div class="project-name">${project.name}</div>
                        <div class="project-type">${project.type}</div>
                        <div class="project-date">Created: ${project.created}</div>
                    </div>
                `).join('');
            } catch (error) {
                console.error('Error loading projects:', error);
            }
        }

        function openProject(projectName) {
            console.log('Opening project:', projectName);
            switchPanel('editor');
        }

        async function createNewProject() {
            const name = prompt('Project name:');
            const type = prompt('Project type (Console/GUI/Web):') || 'Console';

            if (name) {
                try {
                    await window.createProject(name, type);
                    loadProjects();
                } catch (error) {
                    console.error('Error creating project:', error);
                }
            }
        }

        function loadSettings() {
            document.getElementById('theme-select').value = currentTheme;
            document.getElementById('language-select').value = currentLang;
        }
        function parseMarkdown(text) {
            let html = text.replace(/</g, "&lt;").replace(/>/g, "&gt;");

            // Enhanced code block parsing with Ring syntax highlighting
            html = html.replace(/```(ring|Ring)?\n?([\s\S]*?)```/g, (match, lang, code) => {
                const safeCode = code.replace(/</g, '&lt;').replace(/>/g, '&gt;');
                const highlightedCode = highlightRingCode(safeCode.trim());
                return `<pre><button class="copy-btn">${uiStrings[currentLang].copy}</button><code class="ring-code">${highlightedCode}</code></pre>`;
            });

            // Inline code
            html = html.replace(/`([^`]+)`/g, '<code>$1</code>');

            // Bold and italic
            html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
            html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

            // Lists
            html = html.replace(/^\s*[-*]\s+(.*)/gm, '<ul><li>$1</li></ul>');
            html = html.replace(/<\/ul>\s*<ul>/g, '');

            // Line breaks
            html = html.replace(/\n(?!<\/?(pre|code|ul|li|strong|em)>)/g, '<br>');

            return html;
        }

        function highlightRingCode(code) {
            // Ring language syntax highlighting
            let highlighted = code;

            // Keywords
            const keywords = ['func', 'class', 'if', 'else', 'elseif', 'for', 'while', 'switch', 'on', 'off', 'other', 'return', 'break', 'continue', 'try', 'catch', 'done', 'load', 'import', 'package', 'private', 'public', 'new', 'and', 'or', 'not', 'in', 'to', 'step', 'next', 'exit', 'loop', 'again', 'give', 'but', 'bye', 'see', 'put', 'get'];
            keywords.forEach(keyword => {
                const regex = new RegExp(`\\b(${keyword})\\b`, 'gi');
                highlighted = highlighted.replace(regex, '<span class="keyword">$1</span>');
            });

            // Strings
            highlighted = highlighted.replace(/"([^"\\]*(\\.[^"\\]*)*)"/g, '<span class="string">"$1"</span>');
            highlighted = highlighted.replace(/'([^'\\]*(\\.[^'\\]*)*)'/g, '<span class="string">\'$1\'</span>');

            // Numbers
            highlighted = highlighted.replace(/\b\d+(\.\d+)?\b/g, '<span class="number">$&</span>');

            // Comments
            highlighted = highlighted.replace(/#.*$/gm, '<span class="comment">$&</span>');
            highlighted = highlighted.replace(/\/\*[\s\S]*?\*\//g, '<span class="comment">$&</span>');

            // Functions
            highlighted = highlighted.replace(/\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g, '<span class="function">$1</span>(');

            return highlighted;
        }
        function addMessage(text, sender) {
            const log = document.getElementById('chat-log');
            const msgDiv = document.createElement('div');
            msgDiv.className = 'message ' + sender;

            if (sender === 'bot') {
                // Process Maestro HTML and Markdown
                let processedText = processMaestroHTML(text);
                processedText = parseMarkdown(processedText);
                msgDiv.innerHTML = processedText;
            } else {
                msgDiv.innerHTML = text.replace(/</g, "&lt;").replace(/>/g, "&gt;");
            }

            log.appendChild(msgDiv);
            log.scrollTop = log.scrollHeight;

            // Setup copy buttons for regular markdown code blocks
            msgDiv.querySelectorAll('.copy-btn').forEach(btn => {
                btn.onclick = () => {
                    const code = btn.nextElementSibling.textContent;
                    navigator.clipboard.writeText(code);
                    btn.textContent = uiStrings[currentLang].copied;
                    setTimeout(() => { btn.textContent = uiStrings[currentLang].copy; }, 2000);
                };
            });

            // Setup Maestro interactive buttons
            setupMaestroButtons(msgDiv);
        }

        function processMaestroHTML(text) {
            // Process Maestro-specific HTML elements
            let processed = text;

            // Ensure Maestro HTML elements are properly formatted
            processed = processed.replace(/<div class="maestro-code-block">/g, '<div class="maestro-code-block">');
            processed = processed.replace(/<div class="maestro-explanation">/g, '<div class="maestro-explanation">');
            processed = processed.replace(/<div class="maestro-debug">/g, '<div class="maestro-debug">');

            return processed;
        }

        function setupMaestroButtons(msgDiv) {
            // Setup copy buttons for Maestro code blocks
            msgDiv.querySelectorAll('.code-btn').forEach(btn => {
                if (btn.textContent.includes('📋')) {
                    btn.onclick = () => copyCode(btn);
                } else if (btn.textContent.includes('▶️')) {
                    btn.onclick = () => runCode(btn);
                } else if (btn.textContent.includes('✏️')) {
                    btn.onclick = () => editCode(btn);
                }
            });
        }
        async function sendMessage() {
            const input = document.getElementById('msg-input');
            const text = input.value.trim();
            if (text) {
                const startTime = performance.now();

                addMessage(text, 'user');
                input.value = '';
                input.style.height = 'auto';

                // Show typing indicator
                showTypingIndicator();

                try {
                    let botReply;

                    if (window.sendMessageToAI && typeof window.sendMessageToAI === 'function') {
                        botReply = await window.sendMessageToAI(text, currentLang);
                    } else {
                        // Fallback response when Ring backend is not available
                        botReply = getFallbackResponse(text, currentLang);
                    }

                    const endTime = performance.now();
                    const responseTime = Math.round(endTime - startTime);

                    hideTypingIndicator();
                    addMessage(botReply, 'bot');

                    // Show performance info in console
                    console.log(`Response time: ${responseTime}ms`);

                    // Update status if available
                    updatePerformanceStatus(responseTime);
                } catch (error) {
                    hideTypingIndicator();
                    const errorMsg = currentLang === 'ar'
                        ? 'عذراً، حدث خطأ في معالجة طلبك. تأكد من تشغيل التطبيق عبر Ring.'
                        : 'Sorry, there was an error processing your request. Make sure to run the app through Ring.';
                    addMessage(errorMsg, 'bot');
                    console.error('Error:', error);
                }
            }
        }

        function showTypingIndicator() {
            const log = document.getElementById('chat-log');
            const indicator = document.createElement('div');
            indicator.id = 'typing-indicator';
            indicator.className = 'message bot typing';
            indicator.innerHTML = `
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <span class="typing-text">Maestro is thinking...</span>
            `;
            log.appendChild(indicator);
            log.scrollTop = log.scrollHeight;
        }

        function hideTypingIndicator() {
            const indicator = document.getElementById('typing-indicator');
            if (indicator) {
                indicator.remove();
            }
        }

        function updatePerformanceStatus(responseTime) {
            // This could update a status bar or performance indicator
            const status = responseTime < 2000 ? 'Fast' : responseTime < 5000 ? 'Normal' : 'Slow';
            console.log(`Performance: ${status} (${responseTime}ms)`);
        }

        function getFallbackResponse(text, lang) {
            const lowerText = text.toLowerCase();

            if (lang === 'ar') {
                if (lowerText.includes('مرحبا') || lowerText.includes('السلام') || lowerText.includes('أهلا')) {
                    return 'مرحباً! أنا مساعد مايسترو للبرمجة بلغة Ring. كيف يمكنني مساعدتك اليوم؟\n\n**ملاحظة**: للحصول على المساعدة الكاملة للذكاء الاصطناعي، تأكد من تشغيل التطبيق عبر `ring main.ring`';
                }
                if (lowerText.includes('دالة') || lowerText.includes('function')) {
                    return 'إليك مثال على إنشاء دالة في Ring:\n\n```ring\nfunc myFunction(param1, param2)\n    see "Hello from function!" + nl\n    return param1 + param2\n```\n\nلمزيد من المساعدة المتقدمة، تأكد من تشغيل التطبيق عبر Ring.';
                }
                if (lowerText.includes('كلاس') || lowerText.includes('class')) {
                    return 'إليك مثال على إنشاء كلاس في Ring:\n\n```ring\nclass Person\n    cName = ""\n    nAge = 0\n    \n    func init(cName, nAge)\n        this.cName = cName\n        this.nAge = nAge\n    \n    func introduce()\n        see "أنا " + cName + " عمري " + nAge + " سنة" + nl\n```';
                }
                return 'أعتذر، لا يمكنني تقديم إجابة مفصلة في الوضع الحالي. لتفعيل المساعد الذكي الكامل:\n\n1. تأكد من تشغيل التطبيق عبر `ring main.ring`\n2. تحقق من إعداد مفتاح Gemini API\n3. تأكد من الاتصال بالإنترنت\n\nيمكنك استخدام لوحة التوثيق للحصول على مساعدة أساسية.';
            } else {
                if (lowerText.includes('hello') || lowerText.includes('hi') || lowerText.includes('hey')) {
                    return 'Hello! I\'m Maestro Assistant for Ring programming. How can I help you today?\n\n**Note**: For full AI assistance, make sure to run the app via `ring main.ring`';
                }
                if (lowerText.includes('function') || lowerText.includes('func')) {
                    return 'Here\'s an example of creating a function in Ring:\n\n```ring\nfunc myFunction(param1, param2)\n    see "Hello from function!" + nl\n    return param1 + param2\n```\n\nFor advanced AI help, make sure to run the app through Ring.';
                }
                if (lowerText.includes('class')) {
                    return 'Here\'s an example of creating a class in Ring:\n\n```ring\nclass Person\n    cName = ""\n    nAge = 0\n    \n    func init(cName, nAge)\n        this.cName = cName\n        this.nAge = nAge\n    \n    func introduce()\n        see "I\'m " + cName + ", " + nAge + " years old" + nl\n```';
                }
                return 'I apologize, I cannot provide detailed answers in the current mode. To enable full AI assistant:\n\n1. Make sure to run the app via `ring main.ring`\n2. Check Gemini API key configuration\n3. Ensure internet connection\n\nYou can use the Documentation panel for basic help.';
            }
        }
        async function startNewChat() {
            try {
                if (window.clearChatHistory && typeof window.clearChatHistory === 'function') {
                    await window.clearChatHistory();
                }
            } catch (error) {
                console.log('Backend clear history not available');
            }

            const log = document.getElementById('chat-log');
            if (log) {
                log.innerHTML = '';
                // Add welcome message
                const welcomeMessage = currentLang === 'ar'
                    ? 'مرحباً! أنا مساعد مايسترو للبرمجة بلغة Ring. كيف يمكنني مساعدتك اليوم؟'
                    : 'Welcome! I\'m Maestro Assistant for Ring programming. How can I help you today?';
                addMessage(welcomeMessage, 'bot');
            }
        }
        function setTheme(theme) {
            currentTheme = theme;
            document.documentElement.setAttribute('data-theme', theme);
            document.getElementById('theme-toggle').innerHTML = theme === 'dark' ? '<i class="fa-solid fa-sun"></i>' : '<i class="fa-solid fa-moon"></i>';
        }
        function setLanguage(lang) {
            currentLang = lang;
            const strings = uiStrings[lang];
            document.documentElement.lang = lang;
            document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';

            // Update UI elements safely
            const titleElement = document.getElementById('ui-title');
            if (titleElement) titleElement.textContent = strings.title;

            const inputElement = document.getElementById('msg-input');
            if (inputElement) inputElement.placeholder = strings.placeholder;

            const newChatBtn = document.getElementById('new-chat-btn');
            if (newChatBtn) newChatBtn.title = strings.newChat;

            const langToggle = document.getElementById('lang-toggle');
            if (langToggle) langToggle.innerHTML = lang === 'en' ? '<i class="fa-solid fa-language"></i>' : '<i class="fa-solid fa-globe"></i>';

            // Update sidebar items
            updateSidebarLanguage(lang);
        }

        function updateSidebarLanguage(lang) {
            const sidebarItems = document.querySelectorAll('.sidebar-item span');
            const labels = lang === 'ar'
                ? ['مساعد الدردشة', 'محرر الكود', 'المشاريع', 'التوثيق', 'أدوات التطوير', 'التكامل', 'الإعدادات']
                : ['Chat Assistant', 'Code Editor', 'Projects', 'Documentation', 'Dev Tools', 'Integration', 'Settings'];

            sidebarItems.forEach((item, index) => {
                if (labels[index]) {
                    item.textContent = labels[index];
                }
            });
        }
        window.onload = async () => {
            // Initialize sidebar
            initializeSidebar();

            let initialState;
            try {
                if (window.getInitialState && typeof window.getInitialState === 'function') {
                    initialState = await window.getInitialState();
                } else {
                    // Fallback state when Ring backend is not available
                    initialState = {
                        settings: { theme: 'light', language: 'en' },
                        history: []
                    };
                }
            } catch (error) {
                console.log('Backend not available, using default settings');
                initialState = {
                    settings: { theme: 'light', language: 'en' },
                    history: []
                };
            }

            setTheme(initialState.settings.theme);
            setLanguage(initialState.settings.language);

            const log = document.getElementById('chat-log');
            if (log) log.innerHTML = '';

            // Load chat history
            if (initialState.history && Array.isArray(initialState.history)) {
                initialState.history.forEach(item => {
                    if (item && item.role && item.parts && Array.isArray(item.parts) && item.parts.length > 0 && item.parts[0].text) {
                        const sender = item.role === 'user' ? 'user' : 'bot';
                        addMessage(item.parts[0].text, sender);
                    }
                });
            }

            // Add welcome message if no history
            if (!initialState.history || initialState.history.length === 0) {
                const welcomeMessage = currentLang === 'ar'
                    ? 'مرحباً! أنا مساعد مايسترو للبرمجة بلغة Ring. كيف يمكنني مساعدتك اليوم؟\n\n💡 **نصيحة**: للحصول على المساعدة الكاملة للذكاء الاصطناعي، تأكد من تشغيل التطبيق عبر `ring main.ring`'
                    : 'Welcome! I\'m Maestro Assistant for Ring programming. How can I help you today?\n\n💡 **Tip**: For full AI assistance, make sure to run the app via `ring main.ring`';
                addMessage(welcomeMessage, 'bot');
            }
            // Setup event listeners safely
            const msgInput = document.getElementById('msg-input');
            const newChatBtn = document.getElementById('new-chat-btn');
            const themeToggle = document.getElementById('theme-toggle');
            const langToggle = document.getElementById('lang-toggle');
            const sendBtn = document.getElementById('send-btn');

            if (newChatBtn) {
                newChatBtn.addEventListener('click', startNewChat);
            }

            if (themeToggle) {
                themeToggle.addEventListener('click', () => {
                    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
                    setTheme(newTheme);
                    try {
                        if (window.saveSettings) {
                            window.saveSettings(newTheme, currentLang);
                        }
                    } catch (error) {
                        console.log('Settings save not available:', error);
                    }
                });
            }

            if (langToggle) {
                langToggle.addEventListener('click', () => {
                    const newLang = currentLang === 'en' ? 'ar' : 'en';
                    setLanguage(newLang);
                    // Clear chat and show welcome message in new language
                    startNewChat();
                    try {
                        if (window.saveSettings) {
                            window.saveSettings(currentTheme, newLang);
                        }
                    } catch (error) {
                        console.log('Settings save not available:', error);
                    }
                });
            }

            if (sendBtn) {
                sendBtn.addEventListener('click', sendMessage);
            }

            // Setup input field functionality
            if (msgInput) {
                // Auto-resize textarea
                msgInput.addEventListener('input', () => {
                    msgInput.style.height = 'auto';
                    msgInput.style.height = msgInput.scrollHeight + 'px';
                });

                // Send on Enter, new line on Shift+Enter
                msgInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendMessage();
                    }
                });
            }

            // New project button
            const newProjectBtn = document.getElementById('new-project-btn');
            if (newProjectBtn) {
                newProjectBtn.addEventListener('click', createNewProject);
            }

            // Settings event listeners
            const themeSelect = document.getElementById('theme-select');
            const languageSelect = document.getElementById('language-select');

            if (themeSelect) {
                themeSelect.addEventListener('change', (e) => {
                    setTheme(e.target.value);
                    window.saveSettings(e.target.value, currentLang);
                });
            }

            if (languageSelect) {
                languageSelect.addEventListener('change', (e) => {
                    setLanguage(e.target.value);
                    window.saveSettings(currentTheme, e.target.value);
                });
            }

            // Enhanced AI prompts for Ring programming
            addRingProgrammingPrompts();

            // Setup new chat management buttons
            setupChatManagement();
        };

        function setupChatManagement() {
            const saveChatBtn = document.getElementById('save-chat-btn');
            const loadChatBtn = document.getElementById('load-chat-btn');
            const chatMenuBtn = document.getElementById('chat-menu-btn');

            if (saveChatBtn) {
                saveChatBtn.addEventListener('click', saveCurrentConversation);
            }

            if (loadChatBtn) {
                loadChatBtn.addEventListener('click', showConversationsModal);
            }

            if (chatMenuBtn) {
                chatMenuBtn.addEventListener('click', toggleChatMenu);
            }

            // Close menu when clicking outside
            document.addEventListener('click', (e) => {
                const menu = document.getElementById('chat-menu');
                const btn = document.getElementById('chat-menu-btn');
                if (menu && !menu.contains(e.target) && e.target !== btn) {
                    menu.classList.remove('show');
                }
            });
        }

        // ===================================================================
        // Advanced Chat Management Functions
        // ===================================================================
        function toggleChatMenu() {
            const menu = document.getElementById('chat-menu');
            if (menu) {
                menu.classList.toggle('show');
            }
        }

        async function saveCurrentConversation() {
            const name = prompt(currentLang === 'ar' ? 'اسم المحادثة:' : 'Conversation name:');
            if (name && name.trim()) {
                try {
                    if (window.saveConversation) {
                        const result = await window.saveConversation(name.trim());
                        alert(currentLang === 'ar' ? 'تم حفظ المحادثة بنجاح!' : 'Conversation saved successfully!');
                    } else {
                        alert(currentLang === 'ar' ? 'ميزة الحفظ غير متاحة' : 'Save feature not available');
                    }
                } catch (error) {
                    console.error('Error saving conversation:', error);
                    alert(currentLang === 'ar' ? 'خطأ في حفظ المحادثة' : 'Error saving conversation');
                }
            }
            toggleChatMenu();
        }

        async function showConversationsModal() {
            try {
                if (window.getConversations) {
                    const conversations = await window.getConversations();
                    const parsedConversations = typeof conversations === 'string' ? JSON.parse(conversations) : conversations;
                    renderConversationsList(parsedConversations);
                    document.getElementById('conversations-modal').classList.add('show');
                } else {
                    alert(currentLang === 'ar' ? 'ميزة تحميل المحادثات غير متاحة' : 'Load conversations feature not available');
                }
            } catch (error) {
                console.error('Error loading conversations:', error);
                alert(currentLang === 'ar' ? 'خطأ في تحميل المحادثات' : 'Error loading conversations');
            }
            toggleChatMenu();
        }

        function hideConversationsModal() {
            document.getElementById('conversations-modal').classList.remove('show');
        }

        function renderConversationsList(conversations) {
            const list = document.getElementById('conversations-list');
            if (!list) return;

            if (!conversations || conversations.length === 0) {
                list.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: var(--text-secondary);">
                        <i class="fa-solid fa-comments" style="font-size: 48px; margin-bottom: 16px;"></i>
                        <p>${currentLang === 'ar' ? 'لا توجد محادثات محفوظة' : 'No saved conversations'}</p>
                    </div>
                `;
                return;
            }

            list.innerHTML = conversations.map(conv => `
                <div class="conversation-item">
                    <div class="conversation-info">
                        <div class="conversation-name">${conv.name}</div>
                        <div class="conversation-date">${conv.created}</div>
                    </div>
                    <div class="conversation-actions">
                        <button class="conversation-btn" onclick="loadConversation('${conv.id}')">
                            ${currentLang === 'ar' ? 'تحميل' : 'Load'}
                        </button>
                        <button class="conversation-btn delete" onclick="deleteConversation('${conv.id}')">
                            ${currentLang === 'ar' ? 'حذف' : 'Delete'}
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // ===================================================================
        // Code Interaction Functions
        // ===================================================================
        function copyCode(button) {
            const codeBlock = button.closest('.maestro-code-block');
            const codeElement = codeBlock.querySelector('code') || codeBlock.querySelector('pre');
            if (codeElement) {
                const code = codeElement.textContent;
                navigator.clipboard.writeText(code).then(() => {
                    button.innerHTML = '✅ Copied!';
                    setTimeout(() => {
                        button.innerHTML = '📋 Copy';
                    }, 2000);
                });
            }
        }

        async function runCode(button) {
            const codeBlock = button.closest('.maestro-code-block');
            const codeElement = codeBlock.querySelector('code') || codeBlock.querySelector('pre');
            if (codeElement) {
                const code = codeElement.textContent;
                button.innerHTML = '⏳ Running...';

                try {
                    if (window.executeCode) {
                        const result = await window.executeCode(code, 'ring');
                        alert(`Code Output:\n${result}`);
                    } else {
                        alert(currentLang === 'ar' ? 'ميزة تشغيل الكود غير متاحة' : 'Code execution not available');
                    }
                } catch (error) {
                    console.error('Error running code:', error);
                    alert(currentLang === 'ar' ? 'خطأ في تشغيل الكود' : 'Error running code');
                } finally {
                    button.innerHTML = '▶️ Run';
                }
            }
        }

        function editCode(button) {
            const codeBlock = button.closest('.maestro-code-block');
            const codeElement = codeBlock.querySelector('code') || codeBlock.querySelector('pre');
            if (codeElement) {
                const code = codeElement.textContent;

                // Switch to code editor panel and insert code
                switchPanel('editor');

                // Try to insert code into editor
                setTimeout(() => {
                    const editorFrame = document.querySelector('#editor-panel iframe');
                    if (editorFrame && editorFrame.contentWindow) {
                        try {
                            editorFrame.contentWindow.postMessage({
                                type: 'insertCode',
                                code: code
                            }, '*');
                        } catch (error) {
                            console.log('Could not insert code into editor');
                        }
                    }
                }, 500);
            }
        }

        // Listen for messages from code editor
        window.addEventListener('message', (event) => {
            if (event.data.type === 'codeReady') {
                // Code editor is ready to receive code
                console.log('Code editor ready');
            }
        });

        // ===================================================================
        // Ring-JavaScript Integration Functions
        // ===================================================================

        // Function to show notifications (called from Ring)
        window.showNotification = function(title, message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <strong>${title}</strong>
                    <p>${message}</p>
                </div>
                <button class="notification-close" onclick="this.parentElement.remove()">×</button>
            `;

            // Add to page
            document.body.appendChild(notification);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        };

        // Function to update status (called from Ring)
        window.updateStatus = function(message) {
            const statusElement = document.getElementById('status-message');
            if (statusElement) {
                statusElement.textContent = message;
            } else {
                console.log('Status:', message);
            }
        };

        // Function to handle Ring responses
        window.handleRingResponse = function(response, type = 'success') {
            if (type === 'error') {
                showNotification('خطأ', response, 'error');
            } else {
                showNotification('نجح', response, 'success');
            }
        };

        function addRingProgrammingPrompts() {
            const input = document.getElementById('msg-input');
            const suggestions = [
                "How do I create a function in Ring?",
                "Show me a Ring class example",
                "How to work with lists in Ring?",
                "Ring file operations example",
                "How to handle errors in Ring?",
                "Ring GUI programming tutorial",
                "Database connection in Ring",
                "Ring web development basics"
            ];

            // Add placeholder rotation
            let currentSuggestion = 0;
            setInterval(() => {
                if (input.value === '') {
                    input.placeholder = suggestions[currentSuggestion];
                    currentSuggestion = (currentSuggestion + 1) % suggestions.length;
                }
            }, 3000);
        }