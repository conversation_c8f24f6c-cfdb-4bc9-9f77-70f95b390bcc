 let currentPanel = 'git';
        
        // Panel switching functionality
        function switchPanel(panelName) {
            // Update sidebar active state
            document.querySelectorAll('.integration-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-panel="${panelName}"]`).classList.add('active');
            
            // Update panel visibility
            document.querySelectorAll('.integration-panel').forEach(panel => {
                panel.classList.remove('active');
            });
            document.getElementById(panelName).classList.add('active');
            
            currentPanel = panelName;
        }
        
        // Git operations
        function executeGitCommand(command) {
            const output = document.getElementById('git-output');
            output.textContent += `\n$ ${command}\n`;
            
            // Mock git operations - in real implementation, these would call actual git commands
            switch(command) {
                case 'git init':
                    output.textContent += 'Initialized empty Git repository in current directory\n';
                    updateGitStatus('main', '0 changes', 'Repository initialized');
                    break;
                case 'git status':
                    output.textContent += 'On branch main\nnothing to commit, working tree clean\n';
                    break;
                default:
                    output.textContent += `Executed: ${command}\n`;
            }
            
            output.scrollTop = output.scrollHeight;
        }
        
        function updateGitStatus(branch, changes, lastCommit) {
            document.getElementById('current-branch').textContent = branch;
            document.getElementById('git-changes-count').textContent = changes;
            document.getElementById('git-last-commit').textContent = lastCommit;
            
            const statusIndicator = document.getElementById('git-status');
            statusIndicator.className = 'status-indicator status-connected';
            statusIndicator.innerHTML = '<i class="fa-solid fa-circle"></i><span>Git: Connected</span>';
        }
        
        // File operations
        function handleFileImport(files) {
            console.log('Importing files:', files);
            // Implementation for file import
        }
        
        function exportProject(format) {
            console.log('Exporting project as:', format);
            // Implementation for project export
        }
        
        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Panel switching
            document.querySelectorAll('.integration-item').forEach(item => {
                item.addEventListener('click', function() {
                    const panelName = this.getAttribute('data-panel');
                    switchPanel(panelName);
                });
            });
            
            // Git operations
            document.getElementById('git-init-btn').addEventListener('click', () => {
                executeGitCommand('git init');
            });
            
            document.getElementById('git-clone-btn').addEventListener('click', () => {
                const url = document.getElementById('repo-url').value;
                if (url) {
                    executeGitCommand(`git clone ${url}`);
                }
            });
            
            document.getElementById('git-commit-btn').addEventListener('click', () => {
                const message = document.getElementById('commit-message').value;
                if (message) {
                    executeGitCommand(`git commit -m "${message}"`);
                }
            });
            
            // Export operations
            document.querySelectorAll('.export-card').forEach(card => {
                card.addEventListener('click', function() {
                    const format = this.getAttribute('data-format');
                    if (format) {
                        exportProject(format);
                    }
                });
            });
            
            // File drop zone
            const dropZone = document.getElementById('drop-zone');
            const fileInput = document.getElementById('file-input');
            
            dropZone.addEventListener('click', () => {
                fileInput.click();
            });
            
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.classList.add('dragover');
            });
            
            dropZone.addEventListener('dragleave', () => {
                dropZone.classList.remove('dragover');
            });
            
            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.classList.remove('dragover');
                handleFileImport(e.dataTransfer.files);
            });
            
            fileInput.addEventListener('change', (e) => {
                handleFileImport(e.target.files);
            });
            
            // GitHub integration
            document.getElementById('github-connect-btn').addEventListener('click', () => {
                const token = document.getElementById('github-token').value;
                if (token) {
                    const output = document.getElementById('github-output');
                    output.textContent += '\nConnecting to GitHub...\nConnection successful!\n';
                }
            });
        });