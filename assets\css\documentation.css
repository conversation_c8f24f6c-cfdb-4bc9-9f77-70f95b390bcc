:root {
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --bg-tertiary: #e9ecef;
            --border-color: #dee2e6;
            --text-primary: #212529;
            --text-secondary: #6c757d;
            --accent-primary: #007acc;
            --accent-secondary: #28a745;
        }
        
        html[data-theme="dark"] {
            --bg-primary: #1e1e1e;
            --bg-secondary: #252526;
            --bg-tertiary: #2d2d30;
            --border-color: #3e3e42;
            --text-primary: #cccccc;
            --text-secondary: #969696;
            --accent-primary: #007acc;
            --accent-secondary: #4ec9b0;
        }
        
        * {
            box-sizing: border-box;
        }
        
        body {
            margin: 0;
            font-family: 'Inter', sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            height: 100vh;
            display: flex;
        }
        
        .docs-sidebar {
            width: 300px;
            background-color: var(--bg-secondary);
            border-right: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }
        
        .docs-header {
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .docs-title {
            font-size: 18px;
            font-weight: 700;
            color: var(--accent-primary);
            margin: 0;
        }
        
        .docs-subtitle {
            font-size: 12px;
            color: var(--text-secondary);
            margin: 4px 0 0 0;
        }
        
        .search-box {
            margin: 16px 20px;
        }
        
        .search-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            font-size: 14px;
        }
        
        .search-input:focus {
            outline: none;
            border-color: var(--accent-primary);
        }
        
        .docs-nav {
            flex: 1;
            padding: 0 20px 20px 20px;
        }
        
        .nav-category {
            margin-bottom: 16px;
        }
        
        .category-header {
            font-weight: 600;
            font-size: 14px;
            color: var(--text-primary);
            padding: 8px 0;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .category-header:hover {
            color: var(--accent-primary);
        }
        
        .category-icon {
            font-size: 12px;
            transition: transform 0.2s ease;
        }
        
        .category-header.collapsed .category-icon {
            transform: rotate(-90deg);
        }
        
        .category-topics {
            margin-left: 20px;
            border-left: 1px solid var(--border-color);
            padding-left: 12px;
        }
        
        .topic-item {
            padding: 6px 0;
            cursor: pointer;
            font-size: 13px;
            color: var(--text-secondary);
            transition: color 0.2s ease;
        }
        
        .topic-item:hover {
            color: var(--accent-primary);
        }
        
        .topic-item.active {
            color: var(--accent-primary);
            font-weight: 500;
        }
        
        .docs-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .content-header {
            height: 60px;
            background-color: var(--bg-secondary);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
        }
        
        .content-title {
            font-size: 20px;
            font-weight: 600;
            margin: 0;
        }
        
        .content-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            background: none;
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
        }
        
        .action-btn:hover {
            background-color: var(--bg-tertiary);
        }
        
        .content-main {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }
        
        .topic-content {
            max-width: 800px;
        }
        
        .topic-description {
            font-size: 16px;
            line-height: 1.6;
            color: var(--text-secondary);
            margin-bottom: 24px;
        }
        
        .examples-section {
            margin-top: 32px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--text-primary);
        }
        
        .example-item {
            margin-bottom: 24px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .example-header {
            background-color: var(--bg-secondary);
            padding: 12px 16px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .example-title {
            font-weight: 500;
            font-size: 14px;
        }
        
        .example-actions {
            display: flex;
            gap: 8px;
        }
        
        .example-btn {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            transition: all 0.2s ease;
        }
        
        .example-btn:hover {
            background-color: var(--bg-tertiary);
            color: var(--text-primary);
        }
        
        .example-code {
            position: relative;
        }
        
        .example-code pre {
            margin: 0;
            padding: 16px;
            background-color: var(--bg-primary);
            font-family: 'JetBrains Mono', 'Consolas', 'Monaco', monospace;
            font-size: 13px;
            line-height: 1.5;
            overflow-x: auto;
        }
        
        .quick-reference {
            display: none;
        }
        
        .quick-reference.active {
            display: block;
        }
        
        .reference-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-top: 16px;
        }
        
        .reference-card {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .reference-card-header {
            background-color: var(--bg-secondary);
            padding: 12px 16px;
            font-weight: 600;
            font-size: 14px;
        }
        
        .reference-card-content {
            padding: 16px;
        }
        
        .reference-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        .reference-item:last-child {
            border-bottom: none;
        }
        
        .reference-keyword {
            font-family: 'JetBrains Mono', 'Consolas', 'Monaco', monospace;
            font-weight: 600;
            color: var(--accent-primary);
        }
        
        .reference-description {
            font-size: 12px;
            color: var(--text-secondary);
        }
        
        .welcome-content {
            text-align: center;
            padding: 60px 20px;
        }
        
        .welcome-icon {
            font-size: 64px;
            color: var(--accent-primary);
            margin-bottom: 24px;
        }
        
        .welcome-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 16px;
        }
        
        .welcome-description {
            font-size: 16px;
            color: var(--text-secondary);
            line-height: 1.6;
            max-width: 500px;
            margin: 0 auto;
        }