# اختبار تكامل WebView مع الميزات الجديدة

load "webview.ring"

# إنشاء WebView للاختبار
oWebView = new WebView()

oWebView {
    setTitle("Maestro WebView Integration Test")
    setSize(800, 600, WEBVIEW_HINT_NONE)
    
    # ربط دالة اختبار
    bind("testFunction", :testFunction)
    bind("testNotification", :testNotification)
    
    # HTML للاختبار
    setHtml(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>WebView Test</title>
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                button { padding: 10px 20px; margin: 10px; cursor: pointer; }
                .notification {
                    position: fixed; top: 20px; right: 20px;
                    background: #f0f0f0; border: 1px solid #ccc;
                    padding: 15px; border-radius: 5px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                .notification-success { border-left: 4px solid #4CAF50; }
                .notification-error { border-left: 4px solid #f44336; }
            </style>
        </head>
        <body>
            <h1>Maestro WebView Integration Test</h1>
            
            <button onclick="testRingFunction()">Test Ring Function</button>
            <button onclick="testNotificationFromRing()">Test Notification from Ring</button>
            
            <div id="result"></div>
            
            <script>
                // Test calling Ring function
                async function testRingFunction() {
                    try {
                        const result = await window.testFunction('Hello from JavaScript!');
                        document.getElementById('result').innerHTML = 
                            '<p><strong>Result from Ring:</strong> ' + result + '</p>';
                    } catch (error) {
                        console.error('Error calling Ring function:', error);
                    }
                }
                
                // Test notification from Ring
                async function testNotificationFromRing() {
                    try {
                        await window.testNotification();
                    } catch (error) {
                        console.error('Error calling Ring notification:', error);
                    }
                }
                
                // Function to show notifications (called from Ring)
                window.showNotification = function(title, message, type = 'info') {
                    const notification = document.createElement('div');
                    notification.className = 'notification notification-' + type;
                    notification.innerHTML = 
                        '<strong>' + title + '</strong><br>' + message +
                        '<button onclick="this.parentElement.remove()" style="float:right;">×</button>';
                    
                    document.body.appendChild(notification);
                    
                    setTimeout(() => {
                        if (notification.parentElement) {
                            notification.remove();
                        }
                    }, 3000);
                };
            </script>
        </body>
        </html>
    `)
    
    run()
}

# دالة اختبار تستقبل من JavaScript وترد عليه
func testFunction(id, req)
    aArgs = json2list(req)
    cMessage = aArgs[1][1]
    
    see "Received from JavaScript: " + cMessage + nl
    
    cResponse = "Hello from Ring! You sent: " + cMessage
    oWebView.wreturn(id, WEBVIEW_ERROR_OK, '"' + cResponse + '"')

# دالة اختبار الإشعارات
func testNotification(id, req)
    # إرسال إشعار إلى JavaScript
    oWebView.evalJS("window.showNotification('Test from Ring', 'This notification was sent from Ring code!', 'success');")
    
    # إرجاع تأكيد
    oWebView.wreturn(id, WEBVIEW_ERROR_OK, '"Notification sent!"')
