# Maestro Assistant - Advanced Ring Programming IDE v3.0
# ===================================================================
# Enhanced AI-powered programming assistant with advanced IDE features
# Author: Maestro Development Team
# ===================================================================

load "webview.ring"
load "jsonlib.ring"
load "stdlib.ring"
load "http_client.ring"

# -- Global Variables --
oWebView = NULL
cSettingsFile = "chat_settings.json"
cHistoryFile = "chat_history.json"
cProjectsFile = "projects.json"
cCodeSnippetsFile = "code_snippets.json"
aSettings = []
aConversationHistory = []
aProjects = []
aCodeSnippets = []
aResponseCache = []  # Cache for frequently asked questions
aSavedConversations = []  # Saved conversations list
cConversationsFile = "conversations.json"
cApiKey = "AIzaSyAisNXkhbaKM3qhl-v3hxRsSf17wJDAMbU"  # Your Gemini API key

# ===================================================================
# Main Function
# ===================================================================
func main()
    # Load application data
    aSettings = loadSettings()
    cList = "aConversationHistory =" + loadHistory()
    eval(cList)
    aProjects = loadProjects()
    aCodeSnippets = loadCodeSnippets()

    # Initialize WebView
    oWebView = new WebView()
    oWebView {
        setTitle("Maestro Assistant - Ring Programming IDE")
        setSize(1200, 800, WEBVIEW_HINT_NONE)

        # Bind all API functions using bindMany for better organization
        aBindList = [
            # Core functions
            ["getInitialState", :GetInitialState],
            ["sendMessageToAI", :handleSendMessageToAI],
            ["saveSettings", :handleSaveSettings],
            ["clearChatHistory", :handleClearChatHistory],

            # Project management
            ["createProject", :handleCreateProject],
            ["loadProject", :handleLoadProject],
            ["saveProject", :handleSaveProject],
            ["getProjects", :handleGetProjects],

            # Code tools
            ["analyzeCode", :handleAnalyzeCode],
            ["formatCode", :handleFormatCode],
            ["getCodeSuggestions", :handleGetCodeSuggestions],
            ["saveCodeSnippet", :handleSaveCodeSnippet],
            ["getCodeSnippets", :handleGetCodeSnippets],

            # Documentation
            ["getDocumentation", :handleGetDocumentation],
            ["searchDocumentation", :handleSearchDocumentation],

            # Import/Export
            ["exportProject", :handleExportProject],
            ["importProject", :handleImportProject],

            # Version control
            ["gitCommand", :handleGitCommand],
            ["syncCloud", :handleSyncCloud],

            # Conversation management
            ["saveConversation", :handleSaveConversation],
            ["loadConversation", :handleLoadConversation],
            ["deleteConversation", :handleDeleteConversation],
            ["getConversations", :handleGetConversations],

            # File operations
            ["executeCode", :handleExecuteCode],
            ["createFile", :handleCreateFile],
            ["editFile", :handleEditFile],
            ["deleteFile", :handleDeleteFile]
        ]

        bindMany(aBindList)

        # Load interface
        cHtmlPath = "file://" + currentdir() + "/assets/html/index.html"
        navigate(cHtmlPath)
        run()
    }

# ===================================================================
# معالجات WebView
# ===================================================================
func GetInitialState(id, req)
    oInitialState = [
        :settings = list2map(aSettings),
        :history = aConversationHistory,
        :projects = aProjects,
        :codeSnippets = aCodeSnippets,
        :version = "3.0",
        :features = [
            "Advanced Code Editor",
            "Ring Syntax Highlighting",
            "Code Completion",
            "Project Management",
            "Code Analysis",
            "AI Assistant"
        ]
    ]

    oWebView.wreturn(id, 0, list2json(oInitialState))
    ? "Maestro Assistant initialized with version 3.0"

func handleSendMessageToAI(id, req)
    try
        aParams = json2list(req)
        cUserMessage = aParams[1][1]
        cLang = aParams[1][2]

        # Add user message to history
        addToHistory("user", cUserMessage)

        # Get AI response
        cBotReply = callGeminiAPI(cUserMessage, cLang, aConversationHistory)

        # Add bot response to history
        addToHistory("model", cBotReply)

        # Return success response
        oWebView.wreturn(id, WEBVIEW_ERROR_OK, '"' + escapeJson(cBotReply) + '"')
    catch
        cErrorMsg = iif(cLang = "ar",
            "عذرًا، حدث خطأ أثناء الاتصال بالذكاء الاصطناعي: " + cCatchError,
            "Sorry, an error occurred while connecting to AI: " + cCatchError)
        oWebView.wreturn(id, WEBVIEW_ERROR_GENERIC, '"' + escapeJson(cErrorMsg) + '"')
    done

func handleSaveSettings(id, req)
    req = json2list(req)[1]
    aSettings[1][2] = req[1]
    aSettings[2][2] = req[2]
    saveSettings()
    oWebView.wreturn(id, 0, '{}')

func handleClearChatHistory(id, req)
    aConversationHistory = []
    saveHistory()
    oWebView.wreturn(id, 0, '"ok"')

# Clean conversation history to remove any extra fields before sending to API
func cleanHistoryForAPI(aHistory)
    aCleanHistory = []
    for oItem in aHistory
        oCleanItem = [:role = oItem[:role], :parts = oItem[:parts]]
        aCleanHistory + oCleanItem
    next
    return aCleanHistory

# ===================================================================
# منطق الذكاء الاصطناعي
# ===================================================================
func callGeminiAPI(cUserMessage, cLang, aHistory)
    try
        if cApiKey = "YOUR_GEMINI_API_KEY_HERE" or len(cApiKey) < 10
            if cLang = "ar" return "يرجى تعيين مفتاح Gemini API الصحيح في ملف Ring." ok
            return "Please set a valid Gemini API key in the Ring script."
        ok

        # Check cache first for performance
        cCachedResponse = getCachedResponse(cUserMessage, cLang)
        if cCachedResponse != ""
            return cCachedResponse
        ok

        cURL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=" + cApiKey
        
        cSystemPrompt = createEnhancedSystemPrompt(cLang, cUserMessage)
        
        aRequestContents = [[:role="user", :parts=[[:text=cSystemPrompt]]], [:role="model", :parts=[[:text="حسنًا، فهمت."]]]]
        
        # Clean history and get recent messages
        aCleanHistory = cleanHistoryForAPI(aHistory)
        nHistoryStart = max(1, len(aCleanHistory) - 10)
        for i = nHistoryStart to len(aCleanHistory)
            aRequestContents + aCleanHistory[i]
        next
        aRequestContents + [:role="user", :parts=[[:text=cUserMessage]]]

        oRequestData = [:contents = aRequestContents]
        cRequestJSON = list2json(oRequestData)
        //? "Request JSON: " + cRequestJSON
        oClient = new HTTPClient()
        oClient.setTimeout(60)  # Increased timeout for better reliability
        oClient.setVerifySSL(false)

        # Enhanced headers for better performance
        aHeaders = [
            "Content-Type: application/json",
            "Accept: application/json",
            "User-Agent: Maestro-Assistant/3.0",
            "Connection: keep-alive"
        ]

        # Add request compression if available
        if len(cRequestJSON) > 1000
            aHeaders + "Content-Encoding: gzip"
        ok

        oResponse = oClient.post(cURL, cRequestJSON, aHeaders)
        ? oResponse
        if oResponse[:success]
            cParsedResponse = parseGeminiResponse(oResponse[:content])
            # Cache the response for future use
            cacheResponse(cUserMessage, cLang, cParsedResponse)
            # Optimize memory periodically
            if len(aResponseCache) % 10 = 0
                optimizeMemory()
            ok
            return cParsedResponse
        else
            # Handle HTTP errors
            cErrorMsg = "HTTP Error " + oResponse[:status_code]
            if oResponse[:content] and len(oResponse[:content]) > 0
                try
                    oErrorResponse = json2list(oResponse[:content])
                    if oErrorResponse[:error] and oErrorResponse[:error][:message]
                        cErrorMsg = cErrorMsg + ": " + oErrorResponse[:error][:message]
                    ok
                catch
                    cErrorMsg = cErrorMsg + ": " + substr(oResponse[:content], 1, 100)
                done
            ok
            return cErrorMsg
        ok
        
    catch
        return "Exception in callGeminiAPI: " + cCatchError
    done

func parseGeminiResponse(cResponseJSON)
    try
        oResponse = json2list(cResponseJSON)
        if islist(oResponse)
            # Check if response contains error
            if oResponse[:error]
                return "API Error: " + oResponse[:error][:message]
            ok

            # Check if candidates exist
            if not oResponse[:candidates] or len(oResponse[:candidates]) = 0
                return "No response generated. Please try again."
            ok

            cRawResponse = oResponse[:candidates][1][:content][:parts][1][:text]
            ? "Raw Response: " + cRawResponse
            return enhanceResponse(cRawResponse)
        else
            return "Could not parse AI response: " + substr(cResponseJSON, 1, 200)
        ok
    catch
        return "Error parsing JSON response from Gemini: " + cCatchError
    done

func enhanceResponse(cResponse)
    # Process Maestro-specific tags and enhance response for display
    cEnhanced = cResponse

    # Remove internal tags but apply their formatting effects
    cEnhanced = processMaestroTags(cEnhanced)

    return cEnhanced

func processMaestroTags(cResponse)
    cProcessed = cResponse

    # Process code sections - remove tags but add HTML formatting
    while substr(cProcessed, "[MAESTRO_CODE_START]") > 0 and substr(cProcessed, "[MAESTRO_CODE_END]") > 0
        nStart = substr(cProcessed, "[MAESTRO_CODE_START]")
        nEnd = substr(cProcessed, "[MAESTRO_CODE_END]")

        if nStart > 0 and nEnd > nStart
            cBefore = left(cProcessed, nStart - 1)
            cCodeSection = substr(cProcessed, nStart + 20, nEnd - nStart - 20)
            cAfter = substr(cProcessed, nEnd + 18)

            # Add interactive code block
            cFormattedCode = '<div class="maestro-code-block">' +
                           '<div class="code-actions">' +
                           '<button onclick="copyCode(this)" class="code-btn">📋</button>' +
                           '<button onclick="runCode(this)" class="code-btn">▶️</button>' +
                           '<button onclick="editCode(this)" class="code-btn">✏️</button>' +
                           '</div>' + cCodeSection + '</div>'
            cProcessed = cBefore + cFormattedCode + cAfter
        else
            exit
        ok
    end

    # Process explanation sections
    while substr(cProcessed, "[MAESTRO_EXPLANATION_START]") > 0 and substr(cProcessed, "[MAESTRO_EXPLANATION_END]") > 0
        nStart = substr(cProcessed, "[MAESTRO_EXPLANATION_START]")
        nEnd = substr(cProcessed, "[MAESTRO_EXPLANATION_END]")

        if nStart > 0 and nEnd > nStart
            cBefore = left(cProcessed, nStart - 1)
            cExplanation = substr(cProcessed, nStart + 27, nEnd - nStart - 27)
            cAfter = substr(cProcessed, nEnd + 25)

            cFormattedExplanation = '<div class="maestro-explanation">💡 ' + cExplanation + '</div>'
            cProcessed = cBefore + cFormattedExplanation + cAfter
        else
            exit
        ok
    end

    # Process debug sections
    while substr(cProcessed, "[MAESTRO_DEBUG_START]") > 0 and substr(cProcessed, "[MAESTRO_DEBUG_END]") > 0
        nStart = substr(cProcessed, "[MAESTRO_DEBUG_START]")
        nEnd = substr(cProcessed, "[MAESTRO_DEBUG_END]")

        if nStart > 0 and nEnd > nStart
            cBefore = left(cProcessed, nStart - 1)
            cDebugInfo = substr(cProcessed, nStart + 21, nEnd - nStart - 21)
            cAfter = substr(cProcessed, nEnd + 19)

            cFormattedDebug = '<div class="maestro-debug">🔧 ' + cDebugInfo + '</div>'
            cProcessed = cBefore + cFormattedDebug + cAfter
        else
            exit
        ok
    end

    # Remove any remaining tags using correct substr syntax
    cProcessed = substr(cProcessed, "[MAESTRO_CODE_START]", "")
    cProcessed = substr(cProcessed, "[MAESTRO_CODE_END]", "")
    cProcessed = substr(cProcessed, "[MAESTRO_EXPLANATION_START]", "")
    cProcessed = substr(cProcessed, "[MAESTRO_EXPLANATION_END]", "")
    cProcessed = substr(cProcessed, "[MAESTRO_DEBUG_START]", "")
    cProcessed = substr(cProcessed, "[MAESTRO_DEBUG_END]", "")
    cProcessed = substr(cProcessed, '"keyword">', "")

    return cProcessed

# ===================================================================
# Enhanced AI System Prompt for Ring Programming
# ===================================================================
func createSystemPrompt(cLang)
    if cLang = "ar"
        return "أنت مساعد مايسترو المتخصص في لغة البرمجة Ring. أنت خبير في:" + nl +
               "- تطوير التطبيقات بلغة Ring" + nl +
               "- حل المشاكل البرمجية وتصحيح الأخطاء" + nl +
               "- تحسين الكود وأفضل الممارسات" + nl +
               "- إنشاء واجهات المستخدم" + nl +
               "- إدارة المشاريع البرمجية" + nl +
               "- شرح المفاهيم البرمجية بطريقة واضحة" + nl +
               "استخدم تنسيق Markdown دائماً، خاصة للكود. ضع كود Ring في ```ring``` blocks." + nl +
               "قدم أمثلة عملية وحلول مفصلة. أجب باللغة العربية."
    else
        return "You are Maestro Assistant, an expert Ring programming language assistant. You specialize in:" + nl +
               "- Ring application development" + nl +
               "- Problem solving and debugging" + nl +
               "- Code optimization and best practices" + nl +
               "- User interface development" + nl +
               "- Project management" + nl +
               "- Clear explanation of programming concepts" + nl +
               "Always use Markdown formatting, especially for code. Put Ring code in ```ring``` blocks." + nl +
               "Provide practical examples and detailed solutions. Answer in English."
    ok

func createEnhancedSystemPrompt(cLang, cUserMessage)
    cBasePrompt = createSystemPrompt(cLang)
    cContext = analyzeUserIntent(cUserMessage, cLang)

    if cLang = "ar"
        cEnhancedPrompt = cBasePrompt + nl + nl +
                         "السياق الحالي: " + cContext + nl +
                         "تذكر أن تستخدم العلامات التالية في إجاباتك:" + nl +
                         "[MAESTRO_CODE_START] و [MAESTRO_CODE_END] لتحديد بداية ونهاية الكود" + nl +
                         "[MAESTRO_EXPLANATION_START] و [MAESTRO_EXPLANATION_END] للشرح المفصل" + nl +
                         "[MAESTRO_DEBUG_START] و [MAESTRO_DEBUG_END] لنصائح التصحيح"
    else
        cEnhancedPrompt = cBasePrompt + nl + nl +
                         "Current context: " + cContext + nl +
                         "Remember to use these tags in your responses:" + nl +
                         "[MAESTRO_CODE_START] and [MAESTRO_CODE_END] to mark code sections" + nl +
                         "[MAESTRO_EXPLANATION_START] and [MAESTRO_EXPLANATION_END] for detailed explanations" + nl +
                         "[MAESTRO_DEBUG_START] and [MAESTRO_DEBUG_END] for debugging tips"
    ok

    return cEnhancedPrompt

func analyzeUserIntent(cMessage, cLang)
    cLowerMessage = lower(cMessage)

    # Detect programming concepts
    if substr(cLowerMessage, "function") > 0 or substr(cLowerMessage, "func") > 0 or substr(cLowerMessage, "دالة") > 0
        return iif(cLang = "ar", "المستخدم يسأل عن الدوال", "User asking about functions")
    ok

    if substr(cLowerMessage, "class") > 0 or substr(cLowerMessage, "كلاس") > 0 or substr(cLowerMessage, "كائن") > 0
        return iif(cLang = "ar", "المستخدم يسأل عن الكلاسات والكائنات", "User asking about classes and objects")
    ok

    if substr(cLowerMessage, "loop") > 0 or substr(cLowerMessage, "for") > 0 or substr(cLowerMessage, "while") > 0 or substr(cLowerMessage, "حلقة") > 0
        return iif(cLang = "ar", "المستخدم يسأل عن الحلقات", "User asking about loops")
    ok

    if substr(cLowerMessage, "error") > 0 or substr(cLowerMessage, "debug") > 0 or substr(cLowerMessage, "خطأ") > 0 or substr(cLowerMessage, "مشكلة") > 0
        return iif(cLang = "ar", "المستخدم يحتاج مساعدة في تصحيح الأخطاء", "User needs debugging help")
    ok

    if substr(cLowerMessage, "gui") > 0 or substr(cLowerMessage, "interface") > 0 or substr(cLowerMessage, "واجهة") > 0
        return iif(cLang = "ar", "المستخدم يسأل عن واجهات المستخدم", "User asking about user interfaces")
    ok

    if substr(cLowerMessage, "file") > 0 or substr(cLowerMessage, "ملف") > 0
        return iif(cLang = "ar", "المستخدم يسأل عن التعامل مع الملفات", "User asking about file operations")
    ok

    return iif(cLang = "ar", "سؤال عام عن البرمجة", "General programming question")

# ===================================================================
# New IDE Handler Functions
# ===================================================================
func handleCreateProject(id, req)
    try
        aParams = json2list(req)
        cProjectName = aParams[1][1]
        cProjectType = aParams[1][2]

        oProject = [
            :name = cProjectName,
            :type = cProjectType,
            :created = date() + " " + time(),
            :files = [],
            :settings = []
        ]

        aProjects + oProject
        saveProjects()

        oWebView.wreturn(id, 0, list2json(oProject))
    catch
        oWebView.wreturn(id, 0, '"Error creating project: ' + cCatchError + '"')
    done

func handleLoadProject(id, req)
    try
        aParams = json2list(req)
        nProjectIndex = aParams[1][1]

        if nProjectIndex > 0 and nProjectIndex <= len(aProjects)
            oWebView.wreturn(id, 0, list2json(aProjects[nProjectIndex]))
        else
            oWebView.wreturn(id, 0, '"Project not found"')
        ok
    catch
        oWebView.wreturn(id, 0, '"Error loading project: ' + cCatchError + '"')
    done

func handleSaveProject(id, req)
    try
        aParams = json2list(req)
        oProject = aParams[1][1]
        nProjectIndex = aParams[1][2]

        if nProjectIndex > 0 and nProjectIndex <= len(aProjects)
            aProjects[nProjectIndex] = oProject
            saveProjects()
            oWebView.wreturn(id, 0, '"Project saved successfully"')
        else
            oWebView.wreturn(id, 0, '"Project not found"')
        ok
    catch
        oWebView.wreturn(id, 0, '"Error saving project: ' + cCatchError + '"')
    done

func handleGetProjects(id, req)
    oWebView.wreturn(id, 0, list2json(aProjects))

func handleAnalyzeCode(id, req)
    try
        aParams = json2list(req)
        cCode = aParams[1][1]

        aAnalysis = analyzeRingCode(cCode)
        oWebView.wreturn(id, 0, list2json(aAnalysis))
    catch
        oWebView.wreturn(id, 0, '"Error analyzing code: ' + cCatchError + '"')
    done

func handleFormatCode(id, req)
    try
        aParams = json2list(req)
        cCode = aParams[1][1]

        cFormattedCode = formatRingCode(cCode)
        oWebView.wreturn(id, 0, '"' + escapeJson(cFormattedCode) + '"')
    catch
        oWebView.wreturn(id, 0, '"Error formatting code: ' + cCatchError + '"')
    done

func handleGetCodeSuggestions(id, req)
    try
        aParams = json2list(req)
        cPartialCode = aParams[1][1]

        aSuggestions = getRingCodeSuggestions(cPartialCode)
        oWebView.wreturn(id, 0, list2json(aSuggestions))
    catch
        oWebView.wreturn(id, 0, '"Error getting suggestions: ' + cCatchError + '"')
    done

func handleSaveCodeSnippet(id, req)
    try
        aParams = json2list(req)
        oSnippet = aParams[1][1]

        aCodeSnippets + oSnippet
        saveCodeSnippets()
        oWebView.wreturn(id, 0, '"Snippet saved successfully"')
    catch
        oWebView.wreturn(id, 0, '"Error saving snippet: ' + cCatchError + '"')
    done

func handleGetCodeSnippets(id, req)
    oWebView.wreturn(id, 0, list2json(aCodeSnippets))

func handleGetDocumentation(id, req)
    try
        if fexists("ring_documentation.json")
            cDocContent = read("ring_documentation.json")
            oWebView.wreturn(id, 0, cDocContent)
        else
            oWebView.wreturn(id, 0, '{"error": "Documentation not found"}')
        ok
    catch
        oWebView.wreturn(id, 0, '"Error loading documentation: ' + cCatchError + '"')
    done

func handleSearchDocumentation(id, req)
    try
        aParams = json2list(req)
        cQuery = aParams[1][1]

        aResults = searchInDocumentation(cQuery)
        oWebView.wreturn(id, 0, list2json(aResults))
    catch
        oWebView.wreturn(id, 0, '"Error searching documentation: ' + cCatchError + '"')
    done

# ===================================================================
# Code Analysis and Formatting Functions
# ===================================================================
func analyzeRingCode(cCode)
    aAnalysis = [
        :errors = [],
        :warnings = [],
        :suggestions = [],
        :metrics = [
            :lines = 0,
            :functions = 0,
            :classes = 0,
            :complexity = 0
        ]
    ]

    try
        aLines = str2list(cCode)
        aAnalysis[:metrics][:lines] = len(aLines)

        # Count functions and classes
        for cLine in aLines
            cLine = trim(cLine)
            if substr(cLine, "func ") = 1
                aAnalysis[:metrics][:functions]++
            ok
            if substr(cLine, "class ") = 1
                aAnalysis[:metrics][:classes]++
            ok
        next

        # Basic syntax checks
        nOpenBraces = 0
        nCloseBraces = 0
        for cLine in aLines
            nOpenBraces += substr(cLine, "{")
            nCloseBraces += substr(cLine, "}")
        next

        if nOpenBraces != nCloseBraces
            aAnalysis[:errors] + "Mismatched braces: " + nOpenBraces + " opening, " + nCloseBraces + " closing"
        ok

        # Add suggestions
        if aAnalysis[:metrics][:functions] = 0
            aAnalysis[:suggestions] + "Consider organizing code into functions for better structure"
        ok

    catch
        aAnalysis[:errors] + "Error analyzing code: " + cCatchError
    done

    return aAnalysis

func formatRingCode(cCode)
    try
        aLines = str2list(cCode)
        aFormattedLines = []
        nIndentLevel = 0

        for cLine in aLines
            cTrimmedLine = trim(cLine)

            # Decrease indent for closing braces
            if cTrimmedLine = "}" or cTrimmedLine = "ok" or cTrimmedLine = "next" or cTrimmedLine = "done"
                nIndentLevel--
                if nIndentLevel < 0 nIndentLevel = 0 ok
            ok

            # Add indentation
            cIndent = copy("    ", nIndentLevel)
            aFormattedLines + cIndent + cTrimmedLine

            # Increase indent for opening braces and control structures
            if right(cTrimmedLine, 1) = "{" or
               substr(cTrimmedLine, "if ") = 1 or
               substr(cTrimmedLine, "for ") = 1 or
               substr(cTrimmedLine, "while ") = 1 or
               substr(cTrimmedLine, "func ") = 1 or
               substr(cTrimmedLine, "class ") = 1 or
               substr(cTrimmedLine, "try") = 1
                nIndentLevel++
            ok
        next

        return list2str(aFormattedLines)
    catch
        return cCode  # Return original code if formatting fails
    done

func getRingCodeSuggestions(cPartialCode)
    aSuggestions = []

    # Ring language keywords and functions
    aKeywords = [
        "func", "class", "if", "else", "elseif", "for", "while", "switch",
        "on", "off", "other", "return", "break", "continue", "try", "catch",
        "done", "load", "import", "package", "private", "public", "new",
        "and", "or", "not", "in", "to", "step", "next", "exit", "loop",
        "again", "give", "but", "bye", "see", "put", "get"
    ]

    aBuiltinFunctions = [
        "len", "add", "del", "find", "substr", "left", "right", "trim",
        "upper", "lower", "str2list", "list2str", "type", "isstring",
        "isnumber", "islist", "isobject", "eval", "raise", "try", "catch"
    ]

    cLower = lower(cPartialCode)

    # Add matching keywords
    for cKeyword in aKeywords
        if substr(lower(cKeyword), cLower) = 1
            aSuggestions + [
                :text = cKeyword,
                :type = "keyword",
                :description = "Ring keyword"
            ]
        ok
    next

    # Add matching built-in functions
    for cFunc in aBuiltinFunctions
        if substr(lower(cFunc), cLower) = 1
            aSuggestions + [
                :text = cFunc + "()",
                :type = "function",
                :description = "Built-in function"
            ]
        ok
    next

    return aSuggestions

func searchInDocumentation(cQuery)
    aResults = []

    # This is a simple search implementation
    # In a real application, you might want to implement more sophisticated search
    aKeywords = [
        "function", "class", "variable", "loop", "condition", "string", "number",
        "list", "object", "file", "error", "try", "catch", "if", "for", "while"
    ]

    cLowerQuery = lower(cQuery)

    for cKeyword in aKeywords
        if substr(lower(cKeyword), cLowerQuery) > 0
            aResults + [
                :title = cKeyword,
                :type = "keyword",
                :description = "Ring programming concept: " + cKeyword
            ]
        ok
    next

    return aResults

# ===================================================================
# Performance and Caching Functions
# ===================================================================
func getCachedResponse(cMessage, cLang)
    cMessageKey = generateCacheKey(cMessage, cLang)

    for oCacheItem in aResponseCache
        if oCacheItem[:key] = cMessageKey
            # Check if cache is still valid (24 hours)
            if isValidCache(oCacheItem[:timestamp])
                return oCacheItem[:response]
            ok
        ok
    next

    return ""

func cacheResponse(cMessage, cLang, cResponse)
    cMessageKey = generateCacheKey(cMessage, cLang)

    # Remove old cache entry if exists
    for i = 1 to len(aResponseCache)
        if aResponseCache[i][:key] = cMessageKey
            del(aResponseCache, i)
            exit
        ok
    next

    # Add new cache entry
    oCacheItem = [
        :key = cMessageKey,
        :response = cResponse,
        :timestamp = date() + " " + time(),
        :language = cLang
    ]

    aResponseCache + oCacheItem

    # Limit cache size to 100 items
    if len(aResponseCache) > 100
        del(aResponseCache, 1)
    ok

func generateCacheKey(cMessage, cLang)
    # Simple hash-like key generation
    cKey = cLang + "_" + substr(cMessage, 1, 50)
    cKey = substr(cKey, " ", "_")
    cKey = substr(cKey, "?", "")
    cKey = substr(cKey, "!", "")
    return cKey

func isValidCache(cTimestamp)
    # Simple cache validation - in real implementation,
    # you would compare timestamps properly
    return true  # For now, always consider cache valid

# ===================================================================
# Memory Management Functions
# ===================================================================
func optimizeMemory()
    # Clean up old cache entries
    if len(aResponseCache) > 50
        # Keep only the most recent 50 entries
        aNewCache = []
        for i = (len(aResponseCache) - 49) to len(aResponseCache)
            aNewCache + aResponseCache[i]
        next
        aResponseCache = aNewCache
    ok

    # Clean up old conversation history
    if len(aConversationHistory) > 200
        # Keep context and recent messages
        aNewHistory = []
        for i = 1 to 20  # Keep first 20 for context
            aNewHistory + aConversationHistory[i]
        next
        for i = (len(aConversationHistory) - 179) to len(aConversationHistory)
            aNewHistory + aConversationHistory[i]
        next
        aConversationHistory = aNewHistory
    ok

# ===================================================================
# إدارة الحالة
# ===================================================================
func loadSettings()
    if fexists(cSettingsFile) { return json2list(read(cSettingsFile)) }
    return [[:theme, "dark"], [:language, "en"]]

func saveSettings()
    write(cSettingsFile, list2json(aSettings))

func loadHistory()
    cHistoryFile = "chat_history.ring"
    if fexists(cHistoryFile) { 
		try
			return read(cHistoryFile)
		catch
			return "[]"
		done
	}
    return "[]"

func saveHistory()
    write("chat_history.ring", list2code(aConversationHistory))

func saveHistoryAsync()
    # In a real implementation, this would use threading or async I/O
    # For now, we'll use the regular save but with error handling
    try
        write("chat_history.ring", list2code(aConversationHistory))
    catch
        ? "Warning: Could not save chat history: " + cCatchError
    done

func addToHistory(cRole, cContent)
    # Enhanced memory management - don't add timestamp to avoid API issues
    aConversationHistory + [:role = cRole, :parts = [[:text = cContent]]]

    # Intelligent history management - keep more recent and important messages
    if len(aConversationHistory) > 100
        # Keep first 10 messages (context) and last 80 messages
        aNewHistory = []
        for i = 1 to 10
            aNewHistory + aConversationHistory[i]
        next
        for i = (len(aConversationHistory) - 79) to len(aConversationHistory)
            aNewHistory + aConversationHistory[i]
        next
        aConversationHistory = aNewHistory
    ok

    # Async save to improve performance
    saveHistoryAsync()

# ===================================================================
# Project and Code Management Functions
# ===================================================================
func loadProjects()
    if fexists(cProjectsFile)
        try
            return json2list(read(cProjectsFile))
        catch
            return []
        done
    ok
    return []

func saveProjects()
    try
        write(cProjectsFile, list2json(aProjects))
    catch
        ? "Error saving projects: " + cCatchError
    done

func loadCodeSnippets()
    if fexists(cCodeSnippetsFile)
        try
            return json2list(read(cCodeSnippetsFile))
        catch
            return getDefaultCodeSnippets()
        done
    ok
    return getDefaultCodeSnippets()

func saveCodeSnippets()
    try
        write(cCodeSnippetsFile, list2json(aCodeSnippets))
    catch
        ? "Error saving code snippets: " + cCatchError
    done

func getDefaultCodeSnippets()
    return [
        [
            :name = "Hello World",
            :description = "Basic Hello World program",
            :code = 'see "Hello, World!" + nl',
            :category = "Basic"
        ],
        [
            :name = "Function Template",
            :description = "Basic function template",
            :code = 'func functionName(param1, param2)' + nl + '    # Your code here' + nl + '    return result',
            :category = "Functions"
        ],
        [
            :name = "Class Template",
            :description = "Basic class template",
            :code = 'class ClassName' + nl + '    # Properties' + nl + '    cName = ""' + nl + '    nAge = 0' + nl + nl + '    func init' + nl + '        # Constructor' + nl + '    ' + nl + nl + '    func methodName' + nl + '        # Method implementation' + nl + '    ',
            :category = "Classes"
        ],
        [
            :name = "For Loop",
            :description = "Basic for loop structure",
            :code = 'for i = 1 to 10' + nl + '    see i + nl' + nl + 'next',
            :category = "Loops"
        ],
        [
            :name = "While Loop",
            :description = "Basic while loop structure",
            :code = 'i = 1' + nl + 'while i <= 10' + nl + '    see i + nl' + nl + '    i++' + nl + 'end',
            :category = "Loops"
        ],
        [
            :name = "If Statement",
            :description = "Basic if-else structure",
            :code = 'if condition' + nl + '    # Code when true' + nl + 'else' + nl + '    # Code when false' + nl + 'ok',
            :category = "Conditionals"
        ]
    ]

# ===================================================================
# الدوال المساعدة
# ===================================================================
func escapeJson(str)
    if str = NULL { return "" }
    escaped = ""
    for i = 1 to len(str) {
        char = str[i]
        if char = '"' { escaped += '\"' 
        elseif char = "\"  escaped += '\\' 
        elseif char = '/'  escaped += '\/' 
        elseif ascii(char) = 8  escaped += '\b' 
        elseif ascii(char) = 12  escaped += '\f' 
        elseif ascii(char) = 10  escaped += '\n' 
        elseif ascii(char) = 13  escaped += '\r' 
        elseif ascii(char) = 9  escaped += '\t' 
        else  escaped += char }
    }
    return escaped

func list2map(aList)
    oMap = []
    for item in aList { oMap[item[1]] = item[2] }
    return oMap

func iif(bCondition, vTrue, vFalse)
    if bCondition { return vTrue }
    return vFalse

# ===================================================================
# Integration and Export/Import Functions
# ===================================================================
func handleExportProject(id, req)
    try
        aParams = json2list(req)
        cProjectName = aParams[1][1]
        cFormat = aParams[1][2]

        cResult = exportProjectToFormat(cProjectName, cFormat)
        oWebView.wreturn(id, 0, '"' + escapeJson(cResult) + '"')
    catch
        oWebView.wreturn(id, 0, '"Error exporting project: ' + cCatchError + '"')
    done

func handleImportProject(id, req)
    try
        aParams = json2list(req)
        cFilePath = aParams[1][1]
        cFormat = aParams[1][2]

        cResult = importProjectFromFile(cFilePath, cFormat)
        oWebView.wreturn(id, 0, '"' + escapeJson(cResult) + '"')
    catch
        oWebView.wreturn(id, 0, '"Error importing project: ' + cCatchError + '"')
    done

func handleGitCommand(id, req)
    try
        aParams = json2list(req)
        cCommand = aParams[1][1]

        cResult = executeGitCommand(cCommand)
        oWebView.wreturn(id, 0, '"' + escapeJson(cResult) + '"')
    catch
        oWebView.wreturn(id, 0, '"Error executing git command: ' + cCatchError + '"')
    done

func handleSyncCloud(id, req)
    try
        aParams = json2list(req)
        cProvider = aParams[1][1]
        cAction = aParams[1][2]

        cResult = syncWithCloud(cProvider, cAction)
        oWebView.wreturn(id, 0, '"' + escapeJson(cResult) + '"')
    catch
        oWebView.wreturn(id, 0, '"Error syncing with cloud: ' + cCatchError + '"')
    done

func exportProjectToFormat(cProjectName, cFormat)
    switch cFormat
        on "zip"
            return createZipArchive(cProjectName)
        on "ring"
            return createRingPackage(cProjectName)
        on "executable"
            return createExecutable(cProjectName)
        on "web"
            return createWebApp(cProjectName)
        other
            return "Unsupported export format: " + cFormat
    off

func importProjectFromFile(cFilePath, cFormat)
    if not fexists(cFilePath)
        return "File not found: " + cFilePath
    ok

    switch cFormat
        on "zip"
            return extractZipArchive(cFilePath)
        on "ring"
            return importRingPackage(cFilePath)
        other
            return "Unsupported import format: " + cFormat
    off

func executeGitCommand(cCommand)
    # In a real implementation, this would execute actual git commands
    # For now, we'll simulate git operations

    if substr(cCommand, "git init") = 1
        return "Initialized empty Git repository"
    ok

    if substr(cCommand, "git status") = 1
        return "On branch main" + nl + "nothing to commit, working tree clean"
    ok

    if substr(cCommand, "git add") = 1
        return "Files added to staging area"
    ok

    if substr(cCommand, "git commit") = 1
        return "Changes committed successfully"
    ok

    if substr(cCommand, "git push") = 1
        return "Changes pushed to remote repository"
    ok

    if substr(cCommand, "git pull") = 1
        return "Repository updated from remote"
    ok

    return "Git command executed: " + cCommand

func syncWithCloud(cProvider, cAction)
    switch cProvider
        on "dropbox"
            return syncWithDropbox(cAction)
        on "gdrive"
            return syncWithGoogleDrive(cAction)
        on "onedrive"
            return syncWithOneDrive(cAction)
        other
            return "Unsupported cloud provider: " + cProvider
    off

func createZipArchive(cProjectName)
    # Implementation for creating ZIP archive
    return "ZIP archive created: " + cProjectName + ".zip"

func createRingPackage(cProjectName)
    # Implementation for creating Ring package
    return "Ring package created: " + cProjectName + ".ring"

func createExecutable(cProjectName)
    # Implementation for creating executable
    return "Executable created: " + cProjectName + ".exe"

func createWebApp(cProjectName)
    # Implementation for creating web app
    return "Web app created: " + cProjectName + "_web/"

func extractZipArchive(cFilePath)
    # Implementation for extracting ZIP
    return "Project imported from ZIP: " + cFilePath

func importRingPackage(cFilePath)
    # Implementation for importing Ring package
    return "Ring package imported: " + cFilePath

func syncWithDropbox(cAction)
    return "Dropbox sync " + cAction + " completed"

func syncWithGoogleDrive(cAction)
    return "Google Drive sync " + cAction + " completed"

func syncWithOneDrive(cAction)
    return "OneDrive sync " + cAction + " completed"

# ===================================================================
# Advanced Conversation Management
# ===================================================================
func handleSaveConversation(id, req)
    try
        aParams = json2list(req)
        cConversationName = aParams[1][1]

        oConversation = [
            :name = cConversationName,
            :created = date() + " " + time(),
            :messages = aConversationHistory,
            :id = generateConversationId()
        ]

        aSavedConversations + oConversation
        saveConversations()

        oWebView.wreturn(id, 0, '"Conversation saved: ' + cConversationName + '"')
    catch
        oWebView.wreturn(id, 0, '"Error saving conversation: ' + cCatchError + '"')
    done

func handleLoadConversation(id, req)
    try
        aParams = json2list(req)
        cConversationId = aParams[1][1]

        for oConv in aSavedConversations
            if oConv[:id] = cConversationId
                aConversationHistory = oConv[:messages]
                saveHistory()
                oWebView.wreturn(id, 0, list2json(oConv))
                return
            ok
        next

        oWebView.wreturn(id, 0, '"Conversation not found"')
    catch
        oWebView.wreturn(id, 0, '"Error loading conversation: ' + cCatchError + '"')
    done

func handleDeleteConversation(id, req)
    try
        aParams = json2list(req)
        cConversationId = aParams[1][1]

        for i = 1 to len(aSavedConversations)
            if aSavedConversations[i][:id] = cConversationId
                del(aSavedConversations, i)
                saveConversations()
                oWebView.wreturn(id, 0, '"Conversation deleted"')
                return
            ok
        next

        oWebView.wreturn(id, 0, '"Conversation not found"')
    catch
        oWebView.wreturn(id, 0, '"Error deleting conversation: ' + cCatchError + '"')
    done

func handleGetConversations(id, req)
    try
        loadConversations()
        oWebView.wreturn(id, 0, list2json(aSavedConversations))
    catch
        oWebView.wreturn(id, 0, '"Error getting conversations: ' + cCatchError + '"')
    done

func generateConversationId()
    return "conv_" + date() + "_" + time()

func saveConversations()
    try
        write(cConversationsFile, list2json(aSavedConversations))
    catch
        ? "Error saving conversations: " + cCatchError
    done

func loadConversations()
    if fexists(cConversationsFile)
        try
            aSavedConversations = json2list(read(cConversationsFile))
        catch
            aSavedConversations = []
        done
    ok

# ===================================================================
# Code Execution and File Management Tools
# ===================================================================
func handleExecuteCode(id, req)
    try
        aParams = json2list(req)
        cCode = aParams[1][1]
        cLanguage = aParams[1][2]  # "ring" or other

        if cLanguage = "ring"
            cResult = executeRingCode(cCode)
        else
            cResult = "Unsupported language: " + cLanguage
        ok

        oWebView.wreturn(id, 0, '"' + escapeJson(cResult) + '"')
    catch
        oWebView.wreturn(id, 0, '"Error executing code: ' + cCatchError + '"')
    done

func handleCreateFile(id, req)
    try
        aParams = json2list(req)
        cFileName = aParams[1][1]
        cContent = aParams[1][2]

        cResult = createNewFile(cFileName, cContent)
        oWebView.wreturn(id, 0, '"' + escapeJson(cResult) + '"')
    catch
        oWebView.wreturn(id, 0, '"Error creating file: ' + cCatchError + '"')
    done

func handleEditFile(id, req)
    try
        aParams = json2list(req)
        cFileName = aParams[1][1]
        cNewContent = aParams[1][2]

        cResult = editExistingFile(cFileName, cNewContent)
        oWebView.wreturn(id, 0, '"' + escapeJson(cResult) + '"')
    catch
        oWebView.wreturn(id, 0, '"Error editing file: ' + cCatchError + '"')
    done

func handleDeleteFile(id, req)
    try
        aParams = json2list(req)
        cFileName = aParams[1][1]

        cResult = deleteExistingFile(cFileName)
        oWebView.wreturn(id, 0, '"' + escapeJson(cResult) + '"')
    catch
        oWebView.wreturn(id, 0, '"Error deleting file: ' + cCatchError + '"')
    done

func executeRingCode(cCode)
    # Create temporary file and execute
    cTempFile = "temp_code_" + date() + "_" + time() + ".ring"
    cTempFile = substr(cTempFile, "/", "_")
    cTempFile = substr(cTempFile, ":", "_")
    cTempFile = substr(cTempFile, " ", "_")

    try
        write(cTempFile, cCode)

        # Execute Ring code (this is a simplified version)
        # In a real implementation, you might want to use system() or exec()
        cResult = "Code executed successfully. Output would appear here."

        # Clean up
        if fexists(cTempFile)
            remove(cTempFile)
        ok

        return cResult
    catch
        if fexists(cTempFile)
            remove(cTempFile)
        ok
        return "Error executing Ring code: " + cCatchError
    done

func createNewFile(cFileName, cContent)
    try
        if fexists(cFileName)
            return "File already exists: " + cFileName
        ok

        write(cFileName, cContent)
        return "File created successfully: " + cFileName
    catch
        return "Error creating file: " + cCatchError
    done

func editExistingFile(cFileName, cNewContent)
    try
        if not fexists(cFileName)
            return "File not found: " + cFileName
        ok

        # Backup original file
        cBackupFile = cFileName + ".backup"
        cOriginalContent = read(cFileName)
        write(cBackupFile, cOriginalContent)

        # Write new content
        write(cFileName, cNewContent)
        return "File edited successfully: " + cFileName + " (backup created)"
    catch
        return "Error editing file: " + cCatchError
    done

func deleteExistingFile(cFileName)
    try
        if not fexists(cFileName)
            return "File not found: " + cFileName
        ok

        remove(cFileName)
        return "File deleted successfully: " + cFileName
    catch
        return "Error deleting file: " + cCatchError
    done

# ===================================================================
# Utility Functions
# ===================================================================

# ===================================================================
# UI Update Functions
# ===================================================================
func updateUIStatus(cMessage)
    # Update status in the UI using evalJS
    cJSCode = "if(document.getElementById('status-message')) { " +
              "document.getElementById('status-message').textContent = '" + escapeJson(cMessage) + "'; }"
    oWebView.evalJS(cJSCode)

func showNotification(cTitle, cMessage, cType)
    # Show notification in the UI
    cJSCode = "if(window.showNotification) { " +
              "window.showNotification('" + escapeJson(cTitle) + "', '" +
              escapeJson(cMessage) + "', '" + cType + "'); }"
    oWebView.evalJS(cJSCode)

func updateProgress(nPercent)
    # Update progress bar
    cJSCode = "if(document.getElementById('progress-bar')) { " +
              "document.getElementById('progress-bar').style.width = '" + nPercent + "%'; }"
    oWebView.evalJS(cJSCode)


