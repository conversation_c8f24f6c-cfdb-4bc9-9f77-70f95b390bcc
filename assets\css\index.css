@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Tajawal:wght@300;400;500;600;700&family=JetBrains+Mono:wght@300;400;500;600&display=swap');
        :root {
            /* IDE Light Theme */
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --bg-tertiary: #e9ecef;
            --sidebar-bg: #f1f3f4;
            --editor-bg: #ffffff;
            --panel-bg: #f8f9fa;
            --border-color: #dee2e6;
            --border-active: #007acc;
            --text-primary: #212529;
            --text-secondary: #6c757d;
            --text-muted: #adb5bd;
            --accent-primary: #007acc;
            --accent-secondary: #28a745;
            --accent-warning: #ffc107;
            --accent-danger: #dc3545;
            --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
            --user-bubble-bg: linear-gradient(135deg, #007acc, #0056b3);
            --bot-bubble-bg: #f8f9fa;
            --code-bg: #f8f9fa;
            --code-border: #e9ecef;
        }
        html[data-theme="dark"] {
            /* IDE Dark Theme */
            --bg-primary: #1e1e1e;
            --bg-secondary: #252526;
            --bg-tertiary: #2d2d30;
            --sidebar-bg: #252526;
            --editor-bg: #1e1e1e;
            --panel-bg: #252526;
            --border-color: #3e3e42;
            --border-active: #007acc;
            --text-primary: #cccccc;
            --text-secondary: #969696;
            --text-muted: #6a6a6a;
            --accent-primary: #007acc;
            --accent-secondary: #4ec9b0;
            --accent-warning: #ffcc02;
            --accent-danger: #f44747;
            --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.3);
            --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.4);
            --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.5);
            --user-bubble-bg: linear-gradient(135deg, #007acc, #005a9e);
            --bot-bubble-bg: #2d2d30;
            --code-bg: #2d2d30;
            --code-border: #3e3e42;
        }
        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            height: 100vh;
            overflow: hidden;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            display: flex;
            flex-direction: column;
        }

        html[lang="ar"] body {
            font-family: 'Tajawal', sans-serif;
        }

        .ide-container {
            display: flex;
            height: 100vh;
            width: 100%;
        }

        .sidebar {
            width: 60px;
            background-color: var(--sidebar-bg);
            border-right: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px 0;
            transition: width 0.3s ease;
        }

        .sidebar.expanded {
            width: 250px;
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 18px;
            padding: 10px;
            cursor: pointer;
            border-radius: 4px;
            margin-bottom: 20px;
            transition: all 0.2s ease;
        }

        .sidebar-toggle:hover {
            background-color: var(--bg-tertiary);
            color: var(--text-primary);
        }

        .sidebar-menu {
            display: flex;
            flex-direction: column;
            gap: 5px;
            width: 100%;
            padding: 0 10px;
        }

        .sidebar-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: var(--text-secondary);
            text-decoration: none;
        }

        .sidebar-item:hover {
            background-color: var(--bg-tertiary);
            color: var(--text-primary);
        }

        .sidebar-item.active {
            background-color: var(--accent-primary);
            color: white;
        }

        .sidebar-item i {
            font-size: 16px;
            width: 20px;
            text-align: center;
        }

        .sidebar-item span {
            margin-left: 10px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .sidebar.expanded .sidebar-item span {
            opacity: 1;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background-color: var(--bg-secondary);
        }

        .top-bar {
            height: 50px;
            background-color: var(--panel-bg);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }

        .top-bar-title {
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .top-bar-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .workspace {
            flex: 1;
            display: flex;
            overflow: hidden;
        }

        .editor-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            background-color: var(--editor-bg);
        }

        .editor-tabs {
            height: 40px;
            background-color: var(--panel-bg);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            padding: 0 10px;
            overflow-x: auto;
        }

        .editor-tab {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background-color: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-bottom: none;
            border-radius: 4px 4px 0 0;
            cursor: pointer;
            font-size: 13px;
            color: var(--text-secondary);
            margin-right: 2px;
            transition: all 0.2s ease;
        }

        .editor-tab.active {
            background-color: var(--editor-bg);
            color: var(--text-primary);
            border-color: var(--border-active);
        }

        .editor-tab:hover {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
        }

        .editor-container {
            flex: 1;
            position: relative;
            overflow: hidden;
        }
        .chat-window {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            background-color: var(--editor-bg);
            overflow: hidden;
        }
        .chat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 20px;
            background-color: var(--panel-bg);
            border-bottom: 1px solid var(--border-color);
            flex-shrink: 0;
        }
        .header-title {
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
            color: var(--text-primary);
        }
        .header-title i {
            color: var(--accent-primary);
            font-size: 1.2em;
        }
        .header-title h2 {
            margin: 0;
            font-size: 1.1em;
            font-weight: 500;
            color: var(--text-primary);
        }
        .header-controls {
            display: flex;
            gap: 5px;
        }
        .control-btn {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 16px;
            padding: 8px;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.2s ease;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .control-btn:hover {
            background-color: var(--bg-tertiary);
            color: var(--text-primary);
        }
        #chat-log {
            flex-grow: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 16px;
            background-color: var(--editor-bg);
        }
        .message {
            max-width: 85%;
            padding: 12px 16px;
            border-radius: 8px;
            line-height: 1.6;
            color: var(--text-primary);
            animation: popIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            word-wrap: break-word;
            font-size: 14px;
            border: 1px solid var(--border-color);
        }
        .user {
            background: var(--user-bubble-bg);
            color: white;
            align-self: flex-end;
            border-bottom-right-radius: 4px;
            border-color: transparent;
        }
        html[dir="rtl"] .user {
            align-self: flex-start;
            border-bottom-right-radius: 8px;
            border-bottom-left-radius: 4px;
        }
        .bot {
            background: var(--bot-bubble-bg);
            align-self: flex-start;
            border-bottom-left-radius: 4px;
            box-shadow: var(--shadow-sm);
        }
        html[dir="rtl"] .bot {
            align-self: flex-end;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 4px;
        }
        #input-bar {
            display: flex;
            padding: 16px 20px;
            background: var(--panel-bg);
            border-top: 1px solid var(--border-color);
            flex-shrink: 0;
            align-items: flex-end;
            gap: 12px;
        }
        #msg-input {
            flex-grow: 1;
            background: var(--editor-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 14px;
            font-family: 'Inter', sans-serif;
            color: var(--text-primary);
            outline: none;
            transition: border-color 0.2s, box-shadow 0.2s;
            resize: none;
            line-height: 1.4;
            max-height: calc(14px * 1.4 * 4 + 24px);
            overflow-y: auto;
        }
        #msg-input:focus {
            border-color: var(--accent-primary);
            box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
        }
        #send-btn {
            background: var(--accent-primary);
            color: white;
            border: none;
            border-radius: 6px;
            width: 40px;
            height: 40px;
            cursor: pointer;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            flex-shrink: 0;
        }
        #send-btn:hover {
            background: var(--accent-secondary);
            transform: translateY(-1px);
        }
        #send-btn:active {
            transform: translateY(0);
        }
        .message.bot pre {
            background-color: var(--code-bg);
            border: 1px solid var(--code-border);
            border-radius: 6px;
            padding: 16px;
            margin: 12px 0;
            white-space: pre-wrap;
            direction: ltr;
            text-align: left;
            position: relative;
            font-family: 'JetBrains Mono', 'Consolas', 'Monaco', monospace;
            font-size: 13px;
            line-height: 1.5;
            overflow-x: auto;
        }
        .copy-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            background: var(--accent-primary);
            color: white;
            border: none;
            padding: 6px 10px;
            border-radius: 4px;
            cursor: pointer;
            opacity: 0;
            transition: all 0.2s ease;
            font-size: 11px;
            font-weight: 500;
        }
        .copy-btn:hover {
            background: var(--accent-secondary);
            transform: translateY(-1px);
        }
        html[dir="rtl"] .copy-btn {
            right: auto;
            left: 8px;
        }
        .message.bot pre:hover .copy-btn {
            opacity: 1;
        }

        /* Code highlighting styles */
        .message.bot code {
            background-color: var(--code-bg);
            border: 1px solid var(--code-border);
            border-radius: 4px;
            padding: 2px 6px;
            font-family: 'JetBrains Mono', 'Consolas', 'Monaco', monospace;
            font-size: 12px;
        }

        /* Animation keyframes */
        @keyframes popIn {
            0% { opacity: 0; transform: scale(0.8) translateY(10px); }
            100% { opacity: 1; transform: scale(1) translateY(0); }
        }

        @keyframes fadeIn {
            0% { opacity: 0; }
            100% { opacity: 1; }
        }

        /* Scrollbar styling */
        #chat-log::-webkit-scrollbar {
            width: 6px;
        }

        #chat-log::-webkit-scrollbar-track {
            background: var(--bg-secondary);
        }

        #chat-log::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }

        #chat-log::-webkit-scrollbar-thumb:hover {
            background: var(--text-muted);
        }

        /* Ring Code Syntax Highlighting */
        .ring-code .keyword {
            color: var(--accent-primary);
            font-weight: 600;
        }

        .ring-code .string {
            color: var(--accent-secondary);
        }

        .ring-code .number {
            color: #ff6b6b;
        }

        .ring-code .comment {
            color: var(--text-muted);
            font-style: italic;
        }

        .ring-code .function {
            color: #ffd93d;
            font-weight: 500;
        }

        /* Dark theme adjustments for syntax highlighting */
        html[data-theme="dark"] .ring-code .keyword {
            color: #569cd6;
        }

        html[data-theme="dark"] .ring-code .string {
            color: #ce9178;
        }

        html[data-theme="dark"] .ring-code .number {
            color: #b5cea8;
        }

        html[data-theme="dark"] .ring-code .comment {
            color: #6a9955;
        }

        html[data-theme="dark"] .ring-code .function {
            color: #dcdcaa;
        }

        /* Panel Styles */
        .code-editor-panel, .documentation-panel, .projects-panel, .settings-panel, .devtools-panel, .integration-panel {
            width: 100%;
            height: 100%;
            background-color: var(--editor-bg);
        }

        .projects-header, .settings-header {
            height: 50px;
            background-color: var(--panel-bg);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }

        .projects-header h3, .settings-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }

        .projects-list {
            padding: 20px;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 16px;
        }

        .project-card {
            background-color: var(--bot-bubble-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .project-card:hover {
            border-color: var(--accent-primary);
            box-shadow: var(--shadow-sm);
        }

        .project-name {
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 8px;
            color: var(--text-primary);
        }

        .project-type {
            font-size: 12px;
            color: var(--text-secondary);
            background-color: var(--bg-tertiary);
            padding: 2px 8px;
            border-radius: 12px;
            display: inline-block;
            margin-bottom: 8px;
        }

        .project-date {
            font-size: 11px;
            color: var(--text-muted);
        }

        .settings-content {
            padding: 20px;
        }

        .setting-group {
            margin-bottom: 20px;
        }

        .setting-group label {
            display: block;
            font-weight: 500;
            margin-bottom: 8px;
            color: var(--text-primary);
        }

        .setting-group select {
            width: 100%;
            max-width: 200px;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background-color: var(--editor-bg);
            color: var(--text-primary);
            font-size: 14px;
        }

        .setting-group select:focus {
            outline: none;
            border-color: var(--accent-primary);
        }

        /* Typing Indicator */
        .typing {
            opacity: 0.8;
        }

        .typing-dots {
            display: inline-flex;
            gap: 4px;
            margin-right: 8px;
        }

        .typing-dots span {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: var(--accent-primary);
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(1) {
            animation-delay: -0.32s;
        }

        .typing-dots span:nth-child(2) {
            animation-delay: -0.16s;
        }

        .typing-text {
            font-style: italic;
            color: var(--text-secondary);
            font-size: 13px;
        }

        @keyframes typing {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        /* Performance Indicators */
        .performance-badge {
            position: fixed;
            top: 10px;
            right: 10px;
            background-color: var(--accent-success);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .performance-badge.show {
            opacity: 1;
        }

        .performance-badge.fast {
            background-color: var(--accent-success);
        }

        .performance-badge.normal {
            background-color: var(--accent-warning);
        }

        .performance-badge.slow {
            background-color: var(--accent-danger);
        }

        /* Maestro Enhanced Message Styles */
        .maestro-code-block {
            position: relative;
            margin: 8px 0;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
        }

        .code-actions {
            background-color: var(--bg-secondary);
            padding: 8px 12px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            gap: 8px;
        }

        .code-btn {
            background: none;
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.2s ease;
        }

        .code-btn:hover {
            background-color: var(--bg-tertiary);
        }

        .maestro-explanation {
            background-color: rgba(0, 122, 204, 0.1);
            border-left: 4px solid var(--accent-primary);
            padding: 12px;
            margin: 8px 0;
            border-radius: 0 8px 8px 0;
            font-style: italic;
        }

        .maestro-debug {
            background-color: rgba(255, 193, 7, 0.1);
            border-left: 4px solid var(--accent-warning);
            padding: 12px;
            margin: 8px 0;
            border-radius: 0 8px 8px 0;
            font-family: 'JetBrains Mono', 'Consolas', 'Monaco', monospace;
            font-size: 13px;
        }

        /* Chat Management Styles */
        .chat-menu {
            position: absolute;
            top: 50px;
            right: 10px;
            background-color: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            box-shadow: var(--shadow-lg);
            z-index: 1000;
            min-width: 200px;
            display: none;
        }

        .chat-menu.show {
            display: block;
        }

        .chat-menu-item {
            padding: 12px 16px;
            cursor: pointer;
            border-bottom: 1px solid var(--border-color);
            transition: background-color 0.2s ease;
        }

        .chat-menu-item:last-child {
            border-bottom: none;
        }

        .chat-menu-item:hover {
            background-color: var(--bg-secondary);
        }

        .chat-menu-item i {
            margin-right: 8px;
            width: 16px;
        }

        .conversations-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        }

        .conversations-modal.show {
            display: flex;
        }

        .modal-content {
            background-color: var(--bg-primary);
            border-radius: 12px;
            padding: 24px;
            max-width: 500px;
            width: 90%;
            max-height: 70vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: var(--text-secondary);
        }

        .conversation-item {
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .conversation-item:hover {
            background-color: var(--bg-secondary);
            border-color: var(--accent-primary);
        }

        .conversation-info {
            flex: 1;
        }

        .conversation-name {
            font-weight: 500;
            margin-bottom: 4px;
        }

        .conversation-date {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .conversation-actions {
            display: flex;
            gap: 8px;
        }

        .conversation-btn {
            background: none;
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
        }

        .conversation-btn:hover {
            background-color: var(--bg-tertiary);
        }

        .conversation-btn.delete {
            color: var(--accent-danger);
            border-color: var(--accent-danger);
        }

        /* Notification Styles */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 16px;
            box-shadow: var(--shadow-lg);
            z-index: 3000;
            max-width: 300px;
            animation: slideIn 0.3s ease-out;
        }

        .notification-success {
            border-left: 4px solid var(--accent-success);
        }

        .notification-error {
            border-left: 4px solid var(--accent-danger);
        }

        .notification-info {
            border-left: 4px solid var(--accent-primary);
        }

        .notification-warning {
            border-left: 4px solid var(--accent-warning);
        }

        .notification-content {
            margin-bottom: 8px;
        }

        .notification-content strong {
            display: block;
            margin-bottom: 4px;
            font-size: 14px;
        }

        .notification-content p {
            margin: 0;
            font-size: 13px;
            color: var(--text-secondary);
        }

        .notification-close {
            position: absolute;
            top: 8px;
            right: 8px;
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: var(--text-secondary);
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .notification-close:hover {
            color: var(--text-primary);
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }